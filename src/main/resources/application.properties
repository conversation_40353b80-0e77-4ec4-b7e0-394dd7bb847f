logging.level.org.springframework.security=DEBUG
org.springframework.security.web.access=DEBUG
logtracer=ADM
######## SPRING BOOT COMMON SETTINGS ########
spring.main.allow-circular-references=true
server.servlet.jsp.init-parameters.mappedfile=false
spring.main.banner-mode=OFF
spring.security.seckey.value=javax.crypto.spec.SecretKeySpec@fffe8131
######## SPRING MULTIPART SETTINGS ########
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=215MB

############ CONTEXT CONFIGURATION #############
spring.jmx.default-domain=dashboard
spring.application.name=dashboard
server.servlet.context-path=/dashboard
#spring.datasource.login-credential.path=/usr/local/dbsystem.conf.txt 
spring.datasource.login-credential.path=./src/main/resources/dbsystem.conf.txt
############ SETTLEMENT URL ##############
spring.application.settlementurl=https://test.mosambee.in/settlement/autosettle
spring.application.noreplyemail=<EMAIL>

#spring.application.logonTerminal=https://**********:8105/Mosambeelive/webservice/live/logon1
spring.application.initialiseTerminal=https://stage.mosambee.in/mpos/ws/masterParameterDownload1

#spring.application.logonTerminal=https://stage.mosambee.in/mpos/ws/logon1
spring.application.logonTerminal=https://test.mosambee.in/settlement/logon1
spring.application.masterLogon=https://stage.mosambee.in/mpos/masterLogon
#spring.application.masterLogon=https://test.mosambee.in/UATService/masterLogon
spring.application.settlement=http://**********:8105/Mosambeelive/webservice/live/autosettle

##3#email-sms-url####
#spring.application.emailUrl=http://***********:8080/EmailSmsNew/webservice/send/email
spring.application.emailUrl=http://***********:8080/EmailSmsV2.0/webservice/send/email


spring.mvc.converters.preferred-json-mapper=gson

server.port=8081

cpoc.scheduler=0 0 */2 * * ?

 ###### MASTER SLAVE DATASOURCE SETTINGS ########
master.sfn-vas.driverClassName=com.mysql.cj.jdbc.Driver
master.sfn-vas.jdbcUrl=**************************************
master.sfn-vas.username=mosdbadmin
master.sfn-vas.password=M0$fn@dmn5$ac

slave.sfn-vas.driverClassName=com.mysql.cj.jdbc.Driver
slave.sfn-vas.jdbcUrl=**************************************
slave.sfn-vas.username=mosdbadmin
slave.sfn-vas.password=M0$fn@dmn5$ac

master.securityandlicense.driver-class-name=com.mysql.cj.jdbc.Driver
master.securityandlicense.jdbcUrl=*************************************************
master.securityandlicense.username=mosdbadmin
master.securityandlicense.password=M0$fn@dmn5$ac

slave.securityandlicense.driverClassName=com.mysql.cj.jdbc.Driver
slave.securityandlicense.jdbcUrl=*************************************************
slave.securityandlicense.username=mosdbadmin
slave.securityandlicense.password=M0$fn@dmn5$ac

master.sfntransaction.driver-class-name=com.mysql.cj.jdbc.Driver
master.sfntransaction.jdbcUrl=**********************************************
master.sfntransaction.username=mosdbadmin
master.sfntransaction.password=M0$fn@dmn5$ac

slave.sfntransaction.driver-class-name=com.mysql.cj.jdbc.Driver
slave.sfntransaction.jdbcUrl=**********************************************
slave.sfntransaction.username=mosdbadmin
slave.sfntransaction.password=M0$fn@dmn5$ac


master.vernemq-db.driverClassName=com.mysql.cj.jdbc.Driver
master.vernemq-db.jdbcUrl=*****************************************
master.vernemq-db.username=mosdbadmin
master.vernemq-db.password=M0$fn@dmn5$ac

slave.vernemq-db.driverClassName=com.mysql.cj.jdbc.Driver
slave.vernemq-db.jdbcUrl=*****************************************
slave.vernemq-db.username=mosdbadmin
slave.vernemq-db.password=M0$fn@dmn5$ac


slave.sfntransaction-txn.driver-class-name=com.mysql.cj.jdbc.Driver
slave.sfntransaction-txn.jdbcUrl=**********************************************
slave.sfntransaction-txn.username=mosdbadmin
slave.sfntransaction-txn.password=M0$fn@dmn5$ac

master.keyinjection.driver-class-name=com.mysql.cj.jdbc.Driver
master.keyinjection.jdbcUrl=*******************************************
master.keyinjection.username=mosdbadmin
master.keyinjection.password=M0$fn@dmn5$ac


slave.keyinjection.driver-class-name=com.mysql.cj.jdbc.Driver
slave.keyinjection.jdbcUrl=*******************************************
slave.keyinjection.username=mosdbadmin
slave.keyinjection.password=M0$fn@dmn5$ac


######## SESSION & COOKIE SETTINGS ########

server.servlet.session.timeout=30m
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=true

######## WEB SECURITY SETTINGS ########
web-security.props.permit-paths=/webjars/**, /assets/**, /resources/**, /forgot-password, /login/**, /api/**, /reciept/**, /captcha/** , /otp/**
web-security.props.login-page=/login
web-security.props.delete-cookies=JSESSIONID
web-security.props.default-success-url=/merchant/
#web-security.props.default-success-url=/otp/
web-security.props.invalid-session-url=/login?invalid
web-security.props.default-success-landing-url=/merchant/
web-security.props.expired-url=/login?expired

web-security.props.logout-url=/logout
web-security.props.logout-success-url=/login?logout

############## Web Security Settings ##############
web-security.props.origins=http://localhost:8081
web-security.props.methods=GET,POST,PUT
web-security.props.headers=Authorization, Cache-Control, Content-Type

########## JAVA MAIL SENDER CONFIGURATION ###########
spring.mail.host=*************
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=Ms1&iUoOoYgMj

# Other properties
spring.mail.properties.mail.debug=true
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000
spring.mail.properties.mail.transport.protocol=smtp
spring.mail.properties.mail.smtp.starttls.enable=false
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.port=587
spring.mail.properties.mail.smtp.ssl.trust=*

########## EMAIL SMS CONFIG CONFIGURATION ###########
email-sms.config.no-reply-email=<EMAIL>
email-sms.config.sms-gateway1-sender-id=MOSAMB
email-sms.config.sms-gateway1-url=https://alerts.sinfini.com/api/v4/index.php?
email-sms.config.sms-gateway1-working-key=55826epp4cu7i9kw503
email-sms.config.sms-gateway2-password=S5n@$ms1234
email-sms.config.sms-gateway2-senderid=MOSAMB
email-sms.config.sms-gateway2-url=https://www.smsgateway.center/SMSApi/rest/send?
email-sms.config.sms-gateway2-user-id=mosambee
email-sms.config.use-sms-gateway=2

############## Common Environment Settings ##############
common.environment.image-path=https://test.mosambee.in/dashboard/resources/


############## Common DB Settings ##############
sfn.transaction.settlement.common-db-enable=0

# MCASH GET KHATA BALANCE CONFIGURATION
#mcash.khata.balance-api=https://test.mosambee.in/BusinessSolutions/live/khata/getKhataBalance
mcash.khata.balance-api=http://192.168.6.29:8080/BusinessSolutions/live/khata/getKhataBalance
mcash.khata.balance-api-password=RKBLRSC3552I9DVZ49M4FKBLQ
mcash.khata.balance-partner-id=MA14
mcash.khata.balance-api-hash-key=J8lGA3w4eg2KlPBN15+fUQ==

# MCASH GET KHATA TRANSACTIONS CONFIGURATION
#mcash.khata.api=https://test.mosambee.in/BusinessSolutions/live/khata/getKhataTransactions
mcash.khata.api=http://192.168.6.29:8080/BusinessSolutions/live/khata/getKhataTransactions
mcash.khata.api-password=RKBLRSC3552I9DVZ49M4FKBLQ
mcash.khata.partner-id=MA14
mcash.khata.api-hash-key=J8lGA3w4eg2KlPBN15+fUQ==


# MCASH GET KHATA CUSTOMER LEDGER CONFIGURATION
#mcash.khata.customer-api=https://test.mosambee.in/BusinessSolutions/live/khata/getCustomerKhataLedger
mcash.khata.customer-api=http://192.168.6.29:8080/BusinessSolutions/live/khata/getCustomerKhataLedger
mcash.khata.customer-api-password=RKBLRSC3552I9DVZ49M4FKBLQ
mcash.khata.customer-partner-id=MA14
mcash.khata.customer-api-hash-key=J8lGA3w4eg2KlPBN15+fUQ==

templatePath=/usr/local/newbqr/template/
qrOutputPath=/usr/local/newbqr/
qrOutputPathImage=/usr/local/newbqr/image/
qrOutputPathPdf=/usr/local/newbqr/pdf/

# MCASH MERCHANT API CONFIGRATION
mcash.merchantapi.api=http://************:8105/meronboardingapi/mcash/merchant/onboard/
mcash.merchantapi.api-password=RKBLRSC3552I9DVZ49M4FKBLQ
mcash.merchantapi.partner-id=MA14

# INSTANT API CONFIGRATION
#mcash.instant.api=http://**************:8080/PayProWebService/live/updt/bulkUpdateMidTid
mcash.instant.api=http://************:8105/meronboardingapi/mcash/merchant/updateMIDTID
mcash.instant.api-password=RKBLRSC3552I9DVZ49M4FKBLQ
mcash.instant.partner-id=MA14

# SALES WEB APP SERVICE CONFIGRATION #

#sales.web.service.api.check-merchant=https://test.mosambee.in/SalesAppServiceCSS/live/check/aggrMobNoExists
sales.web.service.api.check-merchant=https://test.mosambee.in/SalesAppService/live/check/aggrMobNoExists
sales.web.service.api.partner-id=SFNTEST1
sales.web.service.api.apipwd=RKBLRSC3552I9DVZ49M4FKBLQ

instantPath=https://test.mosambee.in/onlinesignup/user/instant-user-list
instantUnblock=https://test.mosambee.in/onlinesignup/user/instant-unblock-user

#MCASH INSTANT CENTRALISED API CONFIGURATION
mcash.instantcentralised.api=http://***********:8105/onlinesignup/api/merchant/save-yoda-merchant
mcash.instantcentralised.api-password=RKBLRSC3552I9DVZ49M4FKBLQ
mcash.instantcentralised.partner-id=MA14



######## SMS GATEWAY CENTER SETTINGS ########
sms-gateway-center.props.user-id=mosambee
sms-gateway-center.props.password=S5n@$ms1234
sms-gateway-center.props.sender-id=MOSAMB
sms-gateway-center.props.send-method=simpleMsg
sms-gateway-center.props.msg-type=TEXT
sms-gateway-center.props.format=json
sms-gateway-center.props.url=https://www.smsgateway.center/SMSApi/rest/send?
#sms-gateway-center.props.url-new=http://**************:8080/EmailSmsNew/webservice/send/sms
sms-gateway-center.props.url-new=http://***********:8080/EmailSmsNew/webservice/send/sms
sms-gateway-center.props.new-sender-id=MOSAMB
sms-gateway-center.props.new-url=https://alerts.sinfini.com/api/v4/index.php?
sms-gateway-center.props.working-key=55826epp4cu7i9kw503
#AGENT WALLET API CONFIGURATION
addAgentUrl=https://test.mosambee.in/BusinessSolutions/live/AgentWallet/addAgent
addLimitUrl=https://test.mosambee.in/BusinessSolutions/live/AgentWallet/addLimit
key=t0TyYheuW60ZxLlqlQvu7g==
apiPassword=867C4A7E0728C72D1D67A8B86
partnerId=M519936
#TRANSACTION RECIEPT CONFIGURATION
secKey=75487DBF49B450A28E8B1CE33A1B79F9
txnReceiptUrl=https://test.mosambee.in/receipt/transaction/download
# MQ CONFIGURATION
mq.api.add-url=https://test.mosambee.in/notificationengine/device/v1/add-device
mq.api.update-url=https://test.mosambee.in/notificationengine/device/v1/update-device
mq.api.list-url=https://test.mosambee.in/notificationengine/device/v1/get-device-list
mq.api.partner-code=MQ0001
mq.api.hmac-key=FA4184A97EA4BFD376CF076654815664A221BCA5903AFFB7ED81B8DDC6269467
notification.api.add-url=https://test.mosambee.in/notificationengine/notification/v1/message
notification.api.partner-code=MNDASH001
notification.api.hmac-key=48404D635166546A576E5A7234753778214125442A462D4A614E645267556B58
#Ni Api CONFIGURATION
#ni.api.acq-ids=310,284,309
ni.api.acq-ids=NI,TYME,TYMEBANKNI,NIACQ,MPGSNI,NIMPGSJORDAN,NIDUBAI,NINIBSSNIGERIA,NICOMPASSEGYPT
ni.api.url=http://***********:8080/EmailSmsNew/webservice/live/email
ni.api.url-sms=http://***********:8080/EmailSmsNew/webservice/live/sms
#ni.api.url=http://************:8080/EmailSmsV2.0/webservice/live/email
#ni.api.url-sms=http://************:8080/EmailSmsV2.0/webservice/live/sms
spring.application.encryptionkey=C72075333A80E9EAC49D30DC05F5A33A544B1CB77291CD0F84463FF430A7BC73
spring.application.iv=3AEA2A1906804A12599FA72B
spring.application.forgot-pwd=https://test.mosambee.in/merchantapi/forgot-pwd
#spring.application.emailTemplate=http://***********:8080/EmailSmsNew/webservice/send/emailNew
#spring.application.smsTemplate= http://***********:8080/EmailSmsNew/webservice/send/smsNew
#spring.application.emailTemplate=https://test.mosambee.in/EmailSmsV2.0/webservice/live/emailNew
#spring.application.smsTemplate=https://test.mosambee.in/EmailSmsV2.0/webservice/live/smsNew
spring.application.emailTemplate=https://test.mosambee.in/EmailSmsV2.0/webservice/send/emailNew
spring.application.smsTemplate=https://test.mosambee.in/EmailSmsV2.0/webservice/send/smsNew
spring.application.transaction-details=https://test.mosambee.in/mosapi/api/check-status/

spring.application.mintoak.api=https://test.mosambee.in/summaryreport/mintalk/terminal-upload
mintalk-hmac-key=I2OEQ8YMDJA9ZCLNHFT1U7VP0XBW5R6K
spring.application.qrTypes=HDFC-QR,Plain-QR,Co-operative-QR,KOTAK-QR,Aggregator-QR,Global-Payment-QR,IDFC-QR,Yes-QR,SBI-QR,YES-SR600-QR,YES-SR600-HAT-QR,YES-TENT-QR
nameChange=MOSAMBEE
decimalLength=2
currency=INR
amazon-vpa-mcc=1234
amazon-vpa-qrMerchantName=Synergistic financial network pvt Ltd
qrOutputPathAmazon=/usr/local/newbqr/amazon/
qrBasePath=/usr/local/newbqr/
qrUpiOutputPathImage=/usr/local/newupi/image/
qrUpiBasePath=/usr/local/newupi/
upiTemplatePath=/usr/local/newupi/template/
qrUpiOutputPathPdf=/usr/local/newupi/pdf/
spring.application.qrUpiTypes=Plain-QR,Amazon-QR,SBI-QR,KOTAK-OLD-QR,Aggregator-QR,KOTAK-NEW-QR
spring.application.poBoxAcq=TYMEBANKNI,NI,MPGSNI,NIMPGSJORDAN,NICOMPASSEGYPT
deactiveMerchantDetailsPath =/usr/local/vas-dashboard/deactiveMerchant
summaryreport.yesSmsPayApiCall = https://test.mosambee.in/summaryreport/yes/process-mid
yesSmsPay-PartnerCode = WLAGGYE55
spring.application.matCode=NIACQNIACQ,TYMEBANKNI
spring.application.tgEmail=<EMAIL>
device-tid-mapping-api=https://uat.notifypro.in/api/device/v1/registration
device-tid-mapping-lang=en,hi
device-tid-mapping-hmac-key=357538782F4125442A472D4B6150645367566B59703373367639792442264528
soundbox-user-mapping-api=https://uat.notifypro.in/api/device/v1/pair/internal
non-card-txn-list=10,11,19,21,22,24,25,37,56,57
card-txn-list=1,2,7,9,13,14,15,27
# Remove CSS MERCHANT API
css.merchantapi.api=https://test.mosambee.in/channelpartner/css-merchant/remove
pincodeMinLength=6
#EmailSmsCommunication:
send-emailsmscommunication-url=https://test.mosambee.in/summaryreport/send-notification
commission-reference-models=MBAndroid POS=MB P14 ANDROID,MBAndroid POS+QR=MB P14 ANDROID,MBPocket Android POS=MB P12 POCKET ANDROID,MBPocket Android POS+QR=MB P12 POCKET ANDROID,MBSound Box-NFC + DIP Basic=MBSR600 Basic,MBSound Box-NFC + DIP Lite=MBSR600 Lite
mosambee-commission-device-map=P14=MB P14 ANDROID,P12=MB P12 POCKET ANDROID,SR600MINI_DQR=MBSR600 Basic,SR600MINI_DQR=MBSR600 Lite
mosambee-axis-device-map=P5=ANDROID POS,P12=POCKET ANDROID POS,P12=MBPocket Android POS,P12=MBPocket Android POS+QR,P14=MBAndroid POS,P14=MBAndroid POS+QR,P10=PIN ON GLASS,SR600MINI_DQR=MBSound Box-NFC + DIP Basic,SR600MINI_DQR=MBSound Box-NFC + DIP Lite
communicationServiceFlag = yes
tidApproveMail = BANKDHOFARACQ
###DPA#####
merchantIdForDpa=hdfctestmid1
#receipt-url=http://localhost:8080
receipt-url=https://test.mosambee.in/receipt
merchant-dashboard-url=https://test.mosambee.in

vpaSuffix=@appl

upiTg=AIRTELUPI,SETUUPI

spring.application.mintoak_ProgramId=27,37,39,40,95,97,140
setuVpaSuffix=@setuaxis


dukptMap=0=TSS,1=Mutual Auth,2=Successful Mutual Auth,3=TSS to Mutual Auth
upi-txn-url=https://test.mosambee.in/apmrelay/upi/v1/sync-upi-txns?source=


payout-url=https://test.mosambee.in/payout-service/api/v1/payout/process
charge-back-url=https://test.mosambee.in/payout-service/api/v1/payout/other-transactions/upload
payout-deactivate-url=https://test.mosambee.in/payout-service/api/v1/payout/deactivate/bulk



PII.kek.key=iE7Lu0DyHzl1hYDUtt5wvg==
PII.kek.iv=K62iCtPgSifTvRVcph/0Xw==
PII.dek.key.enc=E5Rts+sV6D9sAUyHXjHb/RYXAA7sm4b+ml1Bcbe5mR4=
PII.dek.iv.enc=Od7Yntk6lU6fmROs4O7F9VONHaZnIW/EVXhVTe68p4I=
