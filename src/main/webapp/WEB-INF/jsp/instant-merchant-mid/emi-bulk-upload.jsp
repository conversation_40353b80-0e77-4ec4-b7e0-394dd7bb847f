<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="ISO-8859-1">
<title>DCEMI Terminal Upload</title>
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
<%@ include file="../fragments/header-css.jsp"%>
</head>

<body class="hold-transition sidebar-mini layout-fixed">

	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>
		<div class="content-wrapper">
			<div class="container">
				<c:if test="${ invalid != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						Invalid excel file. Please upload a valid excel file.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<c:if test="${ emi_bulk_upload_format_error != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						Unable to download the terminal upload format.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<div class="card" id="container-card">
					<div class="card-header">
						<div class="row">
							<div class="col">
								<h6 class="mb-0">DCEMI Terminal Upload</h6>
							</div>
							<div class="col-auto">
								<ol class="breadcrumb float-sm-right bg-white mb-0 pl-0"
									id="breadcrumb">
									<li class="breadcrumb-item"><a
										href="${pageContext.servletContext.contextPath}/tg/upload-common"><i
											class="fa fa-backward" aria-hidden="true"></i> Common Upload</a></li>

								</ol>


							</div>
						</div>
						<div class="row d-flex justify-content-end">

							<div class="col-auto">

								<a
									href="${pageContext.servletContext.contextPath}/emi-bulk-upload/mid-tid-upload-format"
									id="download-bulk-upload-format" role="button"
									class="btn btn-primary text-white btn-md float-right"> <em
									class="fas fa-download"></em> Download Terminal Upload Format
								</a>

							</div>
						</div>
					</div>
					<div class="mx-auto col-sm-7">
						<div class="card-body mb-5">
							<form id="emi-bulk-upload" enctype="multipart/form-data"
								action="${pageContext.servletContext.contextPath}/emi-bulk-upload/mid-tid-upload"
								method="post">
								<input type="hidden" name="${_csrf.parameterName}"
									value="${_csrf.token}">
								<div class="row">
									<div class="col-sm-12 col-xs-6" id="submitInput">
										<div class="custom-file">
											<input type="file" class="custom-file-input" id="file"
												name="file"> <label class="custom-file-label"
												for="inputGroupFile01">Choose file</label>
											<div id="file_alert" class="validationAlert"></div>
											<input type="hidden" name="${_csrf.parameterName}"
												value="${_csrf.token}">
										</div>
									</div>
									<div class="col-sm-2 col-xs-6 mt-3 mx-auto d-block"
										id="submitBlock">
										<button class="btn btn-primary mt-5 mt-sm-0  mx-auto d-block"
											type="submit" id="submit">Submit</button>
									</div>
								</div>
							</form>
							<div class="btn btn-primary col-sm-2 mx-auto" id="reloadPage">Upload
								File</div>

							<table class="table table-merchant-listing mt-3">
								<thead class="thead-light">
									<tr>
										<th scope="col" class="items thcol">Value (ROI Condition)</th>

										<th scope="col" class="items thcol">Description</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td class="items" rowspan="1">0</td>
										<td class="items">Not Enabled</td>
									</tr>

									<tr>
										<td class="items" rowspan="1">1</td>
										<td class="items">Only CC - both onus and off-us</td>
									</tr>

									<tr>
										<td class="items" rowspan="1">2</td>
										<td class="items">Only DC - both onus and off-us</td>
									</tr>

									<tr>
										<td class="items" rowspan="1">3</td>
										<td class="items">Only CC onus</td>
									</tr>

									<tr>
										<td class="items" rowspan="1">4</td>
										<td class="items">Only CC off-us</td>
									</tr>

									<tr>
										<td class="items" rowspan="1">5</td>
										<td class="items">Only DC onus</td>
									</tr>

									<tr>
										<td class="items" rowspan="1">6</td>
										<td class="items">Only DC off-us</td>
									</tr>




								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<%@ include file="../fragments/footer-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/instant-merchant-mid/emi-bulk-upload.js"></script>
</body>

</html>