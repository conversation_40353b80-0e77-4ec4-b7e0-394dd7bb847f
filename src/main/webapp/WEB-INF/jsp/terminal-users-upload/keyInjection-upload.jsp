<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">


<head>
<meta charset="ISO-8859-1">
<title>KeyInjection Upload</title>
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
<%@ include file="../fragments/header-css.jsp"%>
</head>

<body class="hold-transition sidebar-mini layout-fixed">

	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>

		<div class="content-wrapper">
			<div class="container">
				<c:if test="${ invalid != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						Invalid excel file. Please upload a valid excel file.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<c:if test="${ merchant_upload_format_error != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						Unable to download the program type upload format.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<div class="card" id="container-card">
					<div class="card-header">
						<div class="row">
							<div class="col">
								<h6 class="mb-0">Key Injection</h6>
							</div>
								<div class="col-auto">
							<ol class="breadcrumb float-sm-right bg-white mb-0 pl-0"
									id="breadcrumb">
									<li class="breadcrumb-item"><a href="${pageContext.servletContext.contextPath}/tg/upload-common"><i
									class="fa fa-backward" aria-hidden="true"></i> Common Upload</a></li>
								
								</ol>
							
								
							</div>
						</div>
						
								
						<div class="row d-flex justify-content-end">
							
							<div class="col-auto">
							<a
									href="${pageContext.servletContext.contextPath}/keyInjection/upload-format"
									id="download-bulk-upload-format" role="button"
									class="btn btn-primary text-white btn-md float-right"> <em
									class="fas fa-download"></em> Download Key Injection Upload Format
								</a>
								
							</div>
						</div>
					</div>
					<div class="card-body mx-auto col-sm-7 mb-1">
						<form id="merchantUpload" enctype="multipart/form-data"
							action="${pageContext.servletContext.contextPath}/keyInjection/keyInjection-upload"
							method="post">
							<input type="hidden" name="${_csrf.parameterName}"
								value="${_csrf.token}">
							<input type="hidden"value='${terminalType}' id="cardType">
							<div class="row">
								<div class="col-sm-12 col-xs-6" id="submitInput">
									<div class="custom-file">
										<input type="file" class="custom-file-input"
											aria-describedby=file name="file" id="file"> <label
											class="custom-file-label" for="file">Choose file</label>
										<div id="file_alert" class="validationAlert"></div>
									</div>
								</div>
								<div class="col-sm-2 col-xs-6 mt-3 mx-auto d-block"
									id="submitBlock">
									<input class="btn btn-primary mt-5 mt-sm-0  mx-auto d-block"
										id="submitUploadMerchant" type="submit" value="Submit" />
								</div>
							</div>
						</form>
						<div class="btn btn-primary" id="reloadPage">Upload File</div>





					</div>
					<div class="upload ml-4 pl-1">
						
					</div>
					<div class="upload ml-4 pl-1 mr-4 d-flex justify-content-center">

					
					<table class="display table table-bordered table-sm w-50" id="table-data">
<thead class="thead-light">
<tr>
<th scope="col" class="items thcol">Id</th>
<th scope="col" class="items thcol">Terminal Type</th>
</tr>
</thead>
<tbody>
<c:forEach items="${terminalType}" var="currents">
<tr>
<td>${currents.key}</td>
<td>${currents.value}</td> 	
</tr>





</c:forEach>

</tbody>
</table> 
					
					

		</div>	
				</div>
			</div>
		</div>
	</div>

	<%@ include file="../fragments/footer-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/program/programTyppe.js"></script>
</body>

</html>