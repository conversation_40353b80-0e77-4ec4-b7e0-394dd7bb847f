<%@ taglib prefix="security" uri="http://www.springframework.org/security/tags" %>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>


<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="ISO-8859-1">
	<title>Blacklist Device</title>
	<%@ include file="../fragments/header-css.jsp"%>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
	<div class="wrapper">
		<%@ include file="../fragments/header.jsp" %>
		<%@ include file="../fragments/aside.jsp" %>
		<div class="content-wrapper">
			<div class="container">
				<div class="card" id="container-card">
				<div class="card-header">
					<div class="row">
						 <h6 class="mb-0">Blacklist Device</h6>
					</div>
				</div>
				<c:if test="${msg==true}">

					<div id="message" class="alert alert-success text-center" >
					Device Blacklisted Successfully.
					<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<c:if test="${msg==false}">

					<div id="message" class="alert alert-danger text-center" >
					Unable to Blacklist Device.
					<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<c:if test="${deviceExist==true}">

					<div id="message" class="alert alert-danger text-center" >
					Device Details Already Exits..!
					<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				
				<div class="card-body">
					<form action="${pageContext.servletContext.contextPath}/auditlogs-reporting/blacklist-device" id="blackListDevice" name = "blackListDevice" method="post" 
						>
						<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}">
						<div class="row ">
							<div class="col-sm-4">
								<div class="form-group input-group-sm">
									<label >Manufacturer<span class="text-danger">*</span></label>
									<input type="text" class="form-control"  id="manufacturer" name = "manufacturer"  placeholder="Manufacturer" maxlength="60" autocomplete="off"/>
								
									<div id="make_error" class="validationAlert" <c:if test="${error.hasFieldErrors('manufacturer') eq true }" > ${error.getFieldError('manufacturer').getDefaultMessage()}</c:if> ></div>
								</div>
							</div>
                            <div class="col-sm-4">
								<div class="form-group input-group-sm">
									<label >Model<span class="text-danger">*</span></label>
									<input type="text" class="form-control"  id="model" name="model" placeholder="Model" maxlength="60" autocomplete="off"/>
									
									<div id="model_error" class="validationAlert" <c:if test="${error.hasFieldErrors('model') eq true }" > ${error.getFieldError('model').getDefaultMessage()}</c:if> ></div>
								</div>
							</div>
								<div class="col-sm-4">
								<div class="form-group input-group-sm">
									<label for="androidVersion">Android Version<span class="text-danger">*</span></label>
									<input type="text"  class="form-control" id="androidVersion" name="androidVersion"  placeholder="Android Version" maxlength="5" autocomplete="off"/>
									
									<div id="androidVersion_error" class="validationAlert" <c:if test="${error.hasFieldErrors('androidVersion') eq true }" > ${error.getFieldError('androidVersion').getDefaultMessage()}</c:if> ></div>
								</div>
							</div>
						</div>
						<div class="row ">
						
                            <div class="col-sm-4">
                                 <div class="form-group input-group-sm">
									<label for="securityPatch">Security Patch<span class="text-danger">*</span></label>
									<input type="text" id="securityPatch" class="form-control" name="securityPatch" placeholder="Security Patch" maxlength="45" autocomplete="off"/>
									
									<div id="securityPatch_error" class="validationAlert" <c:if test="${error.hasFieldErrors('securityPatch') eq true }" > ${error.getFieldError('securityPatch').getDefaultMessage()}</c:if>  ></div>
								</div>
							</div>
							<div class="col-sm-4">
								<div class="form-group input-group-sm">
									<label for="description">Description<span class="text-danger">*</span></label>
									<textarea  rows="2" id="description" class="form-control" name="description" placeholder="Description" maxlength="150" autocomplete="off"/></textarea>
									
									<div id="description_error" class="validationAlert" <c:if test="${error.hasFieldErrors('description') eq true }" > ${error.getFieldError('description').getDefaultMessage()}</c:if> ></div>
								</div>
							</div>
						</div>
						<div class="row ">
							
                           
						</div>
						
						<div class="row mt-3">
							<div class="mx-auto d-block">
								<button type="submit" class="btn btn-primary dvfont mx-auto d-block" value="Submit" id="addTg">
								<i class="far fa-arrow-alt-circle-right" aria-hidden="true"></i>
								 Submit
								</button>
							</div>						
						</div>
						
					</form>
					
				</div>
			</div>
		</div>
	</div>
 </div>
	<%@ include file="../fragments/footer-js.jsp" %>
	<script src="${pageContext.servletContext.contextPath}/resources/js/cpoc/device-blacklist.js"></script>
</body>
</html>
