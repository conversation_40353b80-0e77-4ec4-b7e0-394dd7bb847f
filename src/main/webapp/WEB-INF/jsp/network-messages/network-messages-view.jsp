<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="ISO-8859-1">
<title>Network messages</title>
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
<meta id="app_context_path" name="app_context_path"
	content="${pageContext.servletContext.contextPath}" />
<%@ include file="../fragments/header-css.jsp"%>
<%@ include file="../fragments/datatables-css.jsp"%>
<link
	href="${pageContext.servletContext.contextPath}/resources/css/bootstrap-datepicker-min.css"
	rel="stylesheet" />
</head>

<body class="hold-transition sidebar-mini layout-fixed">
	<div class="wrapper">
		<%@include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>

		<div class="content-wrapper">
			<div class="container">
				<c:if test="${ msg != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						No Data available.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<div class="card" id="container-card">
					<div class="card-header">
						<h6 class="mb-0">Network Messages list</h6>
					</div>
					<div class="card-body">
						<form id="networkmessages_form"
							action="${pageContext.servletContext.contextPath}/networkmessages/networkmessages-list-download"
							method="post" enctype="multipart/form-data">
							<div class="row">
								<div class="col">
									<div class="form-group input-group-sm">
										<label>UserId</label> <input type="text"
											class="form-control no-special-char" id="userId"
											name="userId" value="${userId}" placeholder="UserId"
											maxlength="40" autocomplete="off">
										<div id="userId_error" class="validationAlert"></div>
									</div>
								</div>
								<div class="col">
									<label>From Date </label>
									<div class="input-group input-group-sm">
										<div class="input-group-prepend">
											<span class="input-group-text"> <em
												class="far fa-calendar-alt"></em></span>
										</div>
										<input style="background: white" type="text"
											readonly="readonly" class="form-control" id="fromdate"
											name="fromDate" value="${fromDate}" placeholder="DD/MM/YYYY"
											autocomplete="off">
									</div>
									<div id="fromdate_error" class="validationAlert"></div>
									<div id="30days_error" class="validationAlert"></div>
								</div>
								<div class="col">
									<label>To Date </label>
									<div class="input-group input-group-sm">
										<div class="input-group-prepend">
											<span class="input-group-text"> <em
												class="far fa-calendar-alt"></em></span>
										</div>
										<input style="background: white" type="text"
											class="form-control disableFuturedate" readonly="readonly"
											id="todate" name="toDate" value="${toDate}"
											placeholder="DD/MM/YYYY" autocomplete="off">
									</div>
									<div id="todate_error" class="validationAlert"></div>
									<div id="exceeddays_error" class="validationAlert"></div>
								</div>
							</div>
							<!-- search button -->
							<div class="card-footer pb-0  mt-2 bg-white">
								<div class="row">
									<div class="d-flex mx-auto">
										<button id="search" type="button"
											class="btn btn-md btn-primary mr-2 dvfont" title="Search">
											<em class="fa fa-search"> </em> Search
										</button>
										<button type="button" id="download"
											class="btn btn-md btn-primary mr-2 dvfont">
											<i class="fa fa-download" aria-hidden="true"></i> Download
										</button>
									</div>
								</div>
							</div>
							<!-- /search button -->

							<!-- table list view -->
							<div class="card-body mt-sm-0" id="table_body">
								<div class="table-responsive mt-4">
									<input type="hidden" name="${_csrf.parameterName}"
										value="${_csrf.token}">
									<table id="network_message_table"
										class="display table table-bordered table-sm mx-auto w-100"
										aria-describedby="">
										<thead>
											<tr>
												<th >Sr No.</th>
												<th id="">Username</th>
												<th id="">Terminal ID</th>
												<th id="">HSM Response Code</th>
													<th id="">Response Code</th>
												
												<th id="">Message Type</th>
											
												<th id="">Date</th>
												<th id="">Processing Code</th>
													<th id="">NM Type</th>
												


											</tr>
										</thead>
									</table>
								</div>
							</div>
						</form>
					</div>
				</div>
				<!-- end of table list view -->
			</div>
		</div>
	</div>
	<%@ include file="../fragments/footer-js.jsp"%>
	<%@ include file="../fragments/datatables-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/network-messages/network-messages-view.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/bootstrap-datepicker-min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/validator.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/moment.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/locale/de.js"></script>
</body>
</html>