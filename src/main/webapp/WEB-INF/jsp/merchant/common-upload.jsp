<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>


<!DOCTYPE html>
<html lang="en">
<style>
.select2-results {
	max-height: 248px !important;
}
</style>




<head>

<meta charset="ISO-8859-1">
<title>Merchant Upload</title>
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />


<%@ include file="../fragments/header-css.jsp"%>



<script
	src="${pageContext.servletContext.contextPath}/resources/js/jquery.min.js"></script>
<script
	src="${pageContext.servletContext.contextPath}/resources/js/select2.min.js"></script>

<link
	href="${pageContext.servletContext.contextPath}/resources/css/select2.css"
	rel="stylesheet"></link>





</head>

<body class="hold-transition sidebar-mini layout-fixed">

	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>

		<div class="content-wrapper">
			<div class="container">
				<c:if test="${ invalid != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						Invalid excel file. Please upload a valid excel file.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<c:if test="${ merchant_upload_format_error != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						Unable to download the merchant upload format.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<div class="card" id="container-card">
					<div class="card-header" style="height: 40rem;">
						<div class="row">
							<div class="col" style="display: flex; justify-content: center;">
								<h6 class="mb-0">Upload File</h6>
							</div>
						</div>
						<div class="row"
							style="margin: 22px; display: flex; justify-content: center;">

							<div class="col-5 ">
								<div class="selectRow">

									<select size="1"
										onchange="window.location = jQuery('#urlSelect option:selected').val();"
										id="urlSelect"
										class="js-example-placeholder-multiple js-states singleSelectExample">


										<option>Select a option</option>
										<optgroup label="Merchant" disabled>
											<option
												value="${pageContext.servletContext.contextPath}/merchant/upload-merchant">Merchant
											</option>
												
												

											<option
												value="${pageContext.servletContext.contextPath}/merchant-status/merchant-status-upload">Merchant
												Status</option>
											<option
												value="${pageContext.servletContext.contextPath}/midUpload/upload-view">MID
											</option>

											<option
												value="${pageContext.servletContext.contextPath}/upi-bqr/upi-bqr-upload-view">UPI
												BQR</option>

											<option
												value="${pageContext.servletContext.contextPath}/smsPay/data-upload-view">SMS
												Pay</option>
											<option
												value="${pageContext.servletContext.contextPath}/wallet-upload/wallet-upload-view">Wallet
											</option>
											<option
												value="${pageContext.servletContext.contextPath}/bqr-merchants/upload-bqr-view">BQR
												Merchants</option>
											<option
												value="${pageContext.servletContext.contextPath}/static-qr/upload">Static
												QR</option>
											<option
												value="${pageContext.servletContext.contextPath}/tempMidTid/data-upload-view">Instant
												Registration</option>
											<option
												value="${pageContext.servletContext.contextPath}/offlinemerchants/upload">Offline
												Key</option>

											<option
												value="${pageContext.servletContext.contextPath}/terminal-merchant/status-upload">Merchant/Tid
												Deactivation</option>
											<option
												value="${pageContext.servletContext.contextPath}/dcc-upload/upload-dcc-data">DCC
												Bin Based</option>
											<option
												value="${pageContext.servletContext.contextPath}/dcc-upload/dcc-currency-upload-view">DCC
												Currency Code</option>
											<option
												value="${pageContext.servletContext.contextPath}/deactivated_posId/deactivated_posId_upload">Deactivated
												PosId</option>
											<option
												value="${pageContext.servletContext.contextPath}/merchant/program_type">Program
												Type</option>
											<option
												value="${pageContext.servletContext.contextPath}/css-merchant/css-merchant-view">Remove
												CSS Merchant</option>
											<option
												value="${pageContext.servletContext.contextPath}/commissioning-api/axis-wlpfo-upload-view">AXIS
												WLPFO</option>
										</optgroup>
										<optgroup label="Terminal">
											<option
												value="${pageContext.servletContext.contextPath}/terminal-users/terminal-upload">Terminal
												User</option>
											<option
												value="${pageContext.servletContext.contextPath}/terminal-approve/terminal-approve-upload">Terminal
												Approve</option>
											<option
												value="${pageContext.servletContext.contextPath}/upi-sync/upload-upi-sync">UPI
												Sync</option>

											<option
												value="${pageContext.servletContext.contextPath}/amex-users/amex-upload">AMEX
												User</option>
											<option
												value="${pageContext.servletContext.contextPath}/micro-atm/micro-upload">Micro
												ATM</option>

											<option
												value="${pageContext.servletContext.contextPath}/bank-detail/bank-upload">Bank
												Detail</option>

											<%-- 	<option
												value="${pageContext.servletContext.contextPath}/aeps/aeps-upload">Aeps
											</option> --%>
											<option
												value="${pageContext.servletContext.contextPath}/equitas/equitas-upload">Equitas/Kotak
											</option>

											<option
												value="${pageContext.servletContext.contextPath}/terminal-users/device_mapping">Acquirer
												binding</option>
											<option
												value="${pageContext.servletContext.contextPath}/terminal-users/user_binding">User
												binding</option>
											<option
												value="${pageContext.servletContext.contextPath}/mintoak-upload/mintoak-view?value=0">Mintoak
												Key Sync</option>
											<option
												value="${pageContext.servletContext.contextPath}/mintoak-upload/mintoak-view?value=1">TSS</option>
											<option
												value="${pageContext.servletContext.contextPath}/keyInjection/key-upload">KeyInjection</option>
												
												<option
												value="${pageContext.servletContext.contextPath}/terminal-users/vpa">Regenerate VPA</option>
												

										</optgroup>
										<optgroup label="EMI" disabled>
											<option
												value="${pageContext.servletContext.contextPath}/emi-bulk-upload/mid-tid-upload">Tid/DCEMI
											</option>
											<option
												value="${pageContext.servletContext.contextPath}/emi-conversion/emi-conversion-upload">EMI
												Conversion</option>
											<option
												value="${pageContext.servletContext.contextPath}/emi/emi-upload">EMI
												Configuration</option>
											<option
												value="${pageContext.servletContext.contextPath}/sbi-emi-status-upload/sbi-emi-status-upload">SBI
												EMI STATUS</option>
											<option
												value="${pageContext.servletContext.contextPath}/subvention/upload-subvention-rule-view">Subvention
												Group</option>

										</optgroup>
										<optgroup label="IMS">
											<option
												value="${pageContext.servletContext.contextPath}/ims/ims-upload">IMS
												Posting</option>
										</optgroup>
										<optgroup label="Enterprise" disabled>
											<option
												value="${pageContext.servletContext.contextPath}/enterprise-parent-upload/view">Enterprise
												Bulk</option>
											<option
												value="${pageContext.servletContext.contextPath}/enterprise-upload/upload-view">Assign
												Merchants</option>
										</optgroup>
										<optgroup label="Merchant Specific">
											<option
												value="${pageContext.servletContext.contextPath}/merchant-specific/bulk-upload">Mapping
												URL</option>

											<option
												value="${pageContext.servletContext.contextPath}/merchant-specific/key-bulk-upload">Key
											</option>

										</optgroup>
										<optgroup label="Transactions" disabled>
											<option
												value="${pageContext.servletContext.contextPath}/upi-transactions/upload-upi-view">UPI
												Transactions</option>
											<%-- <option
												value="${pageContext.servletContext.contextPath}/bqr-transactions/bqr-upload">BQR
												Transactions</option> --%>
											<option
												value="${pageContext.servletContext.contextPath}/txn-posting/upload">Transaction
												Posting</option>
													<option
										value="${pageContext.servletContext.contextPath}/charge-back/upload">Other Transaction
									</option>
										</optgroup>
									
										<optgroup label="Settlement">
									
											<option
												value="${pageContext.servletContext.contextPath}/settlement/upload">Manual
												Settlement</option>
												
												<option
												value="${pageContext.servletContext.contextPath}/mpr-report/upload-view">MPR Settlement Report</option>
												<option
												value="${pageContext.servletContext.contextPath}/multi-settlement/upload">Payout</option>
												
												<option
												value="${pageContext.servletContext.contextPath}/statementid-report/upload">Payout Statement Correction</option>
										</optgroup>
										<optgroup label="Configuration" disabled>
											<option
												value="${pageContext.servletContext.contextPath}/binDetails/upload-download-view">Bin
											</option>
											
												<option
												value="${pageContext.servletContext.contextPath}/binDetails/bin-category-upload-view">Bin Category
											</option>
											
											<option
												value="${pageContext.servletContext.contextPath}/binDetails/bin-category-mapping-upload-view">Bin Category Mapping
											</option>
											
											<option
												value="${pageContext.servletContext.contextPath}/api-password-config/mid-bulk-upload">Division
												Group MID Allocation</option>
											<option
												value="${pageContext.servletContext.contextPath}/notification/upload-view">
												Notification</option>

											<option
												value="${pageContext.servletContext.contextPath}/amazon-vpa/amazon-vpa-view">Amazon
												VPA</option>
										<%-- 	<option
												value="${pageContext.servletContext.contextPath}/api-password-config/mid-bulk-upload">Division
												Group MID Allocation</option> --%>
											<option
												value="${pageContext.servletContext.contextPath}/ni-upload/ni-view">Resend
												Notification</option>
											<option
												value="${pageContext.servletContext.contextPath}/enterprise/enterpriseSearch">Blacklisted
												Bin/Card</option>

											<option
												value="${pageContext.servletContext.contextPath}/base/tid-upload-view">Base
												TID Upload</option>

											<option
												value="${pageContext.servletContext.contextPath}/commissioning-api/upload-view">Commission
												API Upload</option>
												
												<option
												value="${pageContext.servletContext.contextPath}/supervisor/supervisor-head-upload">Supervisor
											 Head Upload</option>
												
												<option
												value="${pageContext.servletContext.contextPath}/supervisor/supervisor-upload">Supervisor
											 Upload</option>
											 
											 	

										</optgroup>

										<optgroup label="Location">
											<option
												value="${pageContext.servletContext.contextPath}/location-list/city-upload">City/State
												Configuration</option>

										</optgroup>
										<optgroup label="Sound Box Configuration">
											<option
												value="${pageContext.servletContext.contextPath}/device-tid-mapping/upload-view">Sound
												Box Acquirer Configuration</option>
											<option
												value="${pageContext.servletContext.contextPath}/soundbox-user-mapping/soundbox-user-mapping-view">Sound
												Box User Configuration</option>
										</optgroup>
										<optgroup label="Snapbiz">
											<option
												value="${pageContext.servletContext.contextPath}/snapbizupload/snapbiz-view">Store
												Id</option>
										</optgroup>
										
										<optgroup label="QR">
										<option
											value="${pageContext.servletContext.contextPath}/static-qr/generate-vpa">Offline
											QR</option>
									</optgroup>
										
								


									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>



	<script>
		$("#e1")
				.select2(
						{
							matcher : function(term, text, opt) {
								return text.toUpperCase().indexOf(
										term.toUpperCase()) >= 0
										|| opt.attr("alt").toUpperCase()
												.indexOf(term.toUpperCase()) >= 0;
							}
						});
		/*  $(function() {
			
			
			  var select = $('select');
			
			  select.html(select.find('option').sort(function(x, y) {
			   
			    return $(x).text() > $(y).text() ? 1: -1;
			  }));
			
			  // select default item after sorting (first item)
			  // $('select').get(0).selectedIndex = 0;
			}); */

		function permute(input, permArr, usedChars) {

			var i, ch;
			for (i = 0; i < input.length; i++) {
				ch = input.splice(i, 1)[0];
				usedChars.push(ch);
				if (input.length === 0) {
					permArr.push(usedChars.slice());
				}
				permute(input, permArr, usedChars);
				input.splice(i, 0, ch);
				usedChars.pop();
			}
			return permArr;
		}
		$(".js-example-placeholder-single").select({
			placeholder : "Select a option",

		});
		$(".singleSelectExample").select2(
				{
					matcher : function(term, text) {

						if (term.length === 0)
							return true;
						texts = text.split("0");

						allCombinations = permute(texts, [], []);

						for (i in allCombinations) {
							if (allCombinations[i].join(" ").toUpperCase()
									.indexOf(term.toUpperCase()) === 0) {
								return true;
							}
						}

						return false;
					}
				});
	</script>
	<%@ include file="../fragments/footer-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/merchant/upload-merchant.js">
		
	</script>
</body>

</html>