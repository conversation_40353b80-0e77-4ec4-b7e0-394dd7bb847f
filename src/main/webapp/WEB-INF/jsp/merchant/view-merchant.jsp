<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="ISO-8859-1">
<title>Add Merchant</title>
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
<meta id="app_context_path" name="app_context_path"
	content="${pageContext.servletContext.contextPath}" />
<%@ include file="../fragments/header-css.jsp"%>

<link rel="stylesheet"
	href="${pageContext.servletContext.contextPath}/resources/css/select.min.css">
<style>
.nav-link.active {
	color: white !important;
	background-color: #464855 !important;
	border-color: #dee2e6 #dee2e6 #fff;
}
</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed">
	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>


		<div class="content-wrapper">
			<div id="update-merchant"></div>

			<div class="container-fluid">
				<input type="hidden" id="merchantId"
					value="${merchantBusinessFormBean.merchantId}"> <input
					type="hidden" id="merchantName"
					value="${merchantBusinessFormBean.businessName}">
				<div class="table-responsive">

					<div class="container-fluid mt-3">
						<!-- Nav Tabs for Business and Limit Information -->
						<ul class="nav nav-tabs" id="myTab" role="tablist"
							style="background: #d1d1d1; height: auto;">
							<!-- Business Information Tab -->
							<li class="nav-item"><a class="nav-link active"
								id="businessInfoTab" data-toggle="tab" href="#businessInfo"
								role="tab" aria-controls="businessInfo" aria-selected="true"
								style="color: white;"> Business Information </a></li>

							<!-- Limit Information Tab -->
							<li class="nav-item"><a class="nav-link" id="limitInfoTab"
								data-toggle="tab" href="#limitInfo" role="tab"
								aria-controls="limitInfo" aria-selected="false"
								style="color: white;"> Limit Information </a></li>
						</ul>

						<!-- Tab Content -->
						<div class="tab-content mt-3" id="myTabContent">
							<!-- Business Information Tab Content -->
							<div class="tab-pane fade show active" id="businessInfo"
								role="tabpanel" aria-labelledby="businessInfoTab">
								<!-- Business Information Table -->
								<table class="table table-striped">
									<thead class="whitesmoke"></thead>
									<tbody>
										<tr id="businessInfoCollapse" class="collapse">
											<td colspan="3">
												<!-- Display Business Information Here -->
												<table class="table table-striped">
													<tbody>
														<tr>
															<th>Name</th>
															<td>${merchantBusinessFormBean.businessName}</td>
														</tr>
														<tr>
															<th>E-mail</th>
															<td><c:if test="${userBean.merchant1 ne '1'}">
			                                                XXXXX
			                                            </c:if> <c:if
																	test="${userBean.merchant1 eq '1'}">
			                                                ${merchantBusinessFormBean.businessEmail}
			                                            </c:if></td>
														</tr>
														<th>Address</th>
														<c:if test="${userBean.merchant1 ne '1'}">
															<td>XXXXX</td>
														</c:if>
														<c:if test="${userBean.merchant1 eq '1'}">
															<td>${ merchantBusinessFormBean.address }<c:if
																	test="${merchantBusinessFormBean.city ne  '0'}"> ${merchantBusinessFormBean.cityCode},</c:if>
																<c:if test="${merchantBusinessFormBean.state ne  '0'}"> ${merchantBusinessFormBean.stateCode},</c:if>
																<c:if test="${merchantBusinessFormBean.country ne  '0'}"> ${merchantBusinessFormBean.countryCode}</c:if>
															</td>
														</c:if>
														</tr>
														<tr>
															<th>Contact Number</th>
															<c:if test="${userBean.merchant1 ne '1'}">
																<td>XXXXX</td>
															</c:if>
															<c:if test="${userBean.merchant1 eq '1'}">
																<td>${ merchantBusinessFormBean.contactNo }</td>
															</c:if>
														</tr>

														<tr>
															<th>Merchant Code</th>
															<td>${ merchantBusinessFormBean.merchantCode }</td>
														</tr>
														<tr>
															<th>Merchant Key</th>
															<td>${ merchantBusinessFormBean.merchantKey }</td>
														</tr>



														<tr>
															<th>Posting Code</th>
															<td>${ merchantBusinessFormBean.postingCode }</td>
														</tr>


														<tr>
															<th>Posting Key</th>
															<td>${ merchantBusinessFormBean.postingKey }</td>
														</tr>

														<tr>
															<th>Receipt Address</th>
															<td><c:choose>
																	<c:when
																		test="${merchantBusinessFormBean.receiptAddress eq 0}">
                Merchant level name &amp; address (default)
            </c:when>
																	<c:when
																		test="${merchantBusinessFormBean.receiptAddress eq 1}">
                Terminal level name &amp; address (no fallback)
            </c:when>
																	<c:when
																		test="${merchantBusinessFormBean.receiptAddress eq 2}">
                Terminal level name &amp; address (fallback to merchant level)
            </c:when>
																	<c:when
																		test="${merchantBusinessFormBean.receiptAddress eq 3}">
                Merchant level name &amp; terminal level address (no fallback)
            </c:when>
																	<c:when
																		test="${merchantBusinessFormBean.receiptAddress eq 4}">
                Merchant level name &amp; terminal level address (address fallback to merchant level)
            </c:when>
																	<c:otherwise>
               NAS
            </c:otherwise>
																</c:choose></td>
														</tr>

													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<!-- Limit Information Tab Content -->
							<div class="tab-pane fade" id="limitInfo" role="tabpanel"
								aria-labelledby="limitInfoTab">
								<!-- Limit Information Table -->
								<div class="row">
									<!-- Left column (col-6) -->
									<div class="col-6">
										<table class="table table-striped">
											<thead class="whitesmoke"></thead>
											<tbody>
												<tr>
													<th>MCC</th>
													<td>${merchantBusinessFormBean.mcc}</td>
												</tr>
												<tr>
													<th>MDR</th>
													<td>${merchantBusinessFormBean.mdr}</td>
												</tr>
												<tr>
													<th>Service Charge</th>
													<td>${merchantBusinessFormBean.serviceCharges}</td>
												</tr>
												<tr>
													<th>Allowed Distance</th>
													<td>${merchantBusinessFormBean.allowedDistance}</td>
												</tr>
												<tr>
													<th>Max Transaction Per Hour</th>
													<td>${merchantBusinessFormBean.maxTransactionPerHour}</td>
												</tr>
												<tr>
													<th>Day Limit</th>
													<td>${merchantBusinessFormBean.dailyLimit}</td>
												</tr>
												<tr>
													<th>Max Transaction Value</th>
													<td>${merchantBusinessFormBean.maxTransactionValue}</td>
												</tr>
												<tr>
													<th>Description Required</th>
													<td><c:if
															test="${merchantBusinessFormBean.descriptionRequired eq 0}">No</c:if>
														<c:if
															test="${merchantBusinessFormBean.descriptionRequired eq 1}">Yes</c:if>
													</td>
												</tr>
												<tr>
													<th>Daily Report Required</th>
													<td><c:if
															test="${merchantBusinessFormBean.dailyReportRequired eq 0}">Generic</c:if>
														<c:if
															test="${merchantBusinessFormBean.dailyReportRequired eq 1}">Customized</c:if>
														<c:if
															test="${merchantBusinessFormBean.dailyReportRequired eq 2}">No Reports</c:if>
													</td>
												</tr>
												<tr>
													<th>Allow Cash & Cheque</th>
													<td><c:if
															test="${merchantBusinessFormBean.reportType eq 0}">Yes</c:if>
														<c:if test="${merchantBusinessFormBean.reportType eq 1}">No</c:if>
													</td>
												</tr>
												<tr>
													<th>Auto Settlement</th>
													<td><c:if
															test="${merchantBusinessFormBean.autoSettlement eq 0}">Yes</c:if>
														<c:if
															test="${merchantBusinessFormBean.autoSettlement eq 1}">No</c:if>
													</td>
												</tr>
												<tr>
													<th>Debit-Only Sale</th>
													<td><c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 0}">No Check (Default value)</c:if>
														<c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 1}">Domestic Debit Only</c:if>
														<c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 2}">Domestic Debit Only</c:if>
														<c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 3}">Foreign Debit Only</c:if>
														<c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 4}">Foreign Credit Only</c:if>
														<c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 5}">Debit only (ALL FD and DD)</c:if>
														<c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 6}">Credit only (ALL FC and DC)</c:if>
														<c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 7}">Foreign Credit and Debit only (No domestic processing)</c:if>
														<c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 8}">Domestic Credit and Debit only (No Foreign processing)</c:if>
														<c:if
															test="${merchantBusinessFormBean.debitOnlySale eq 9}">Neither Domestic nor Foreign</c:if>
													</td>
												</tr>
											</tbody>
										</table>
									</div>

									<!-- Right column (col-6) -->
									<div class="col-6">
										<table class="table table-striped">
											<thead class="whitesmoke"></thead>
											<tbody>

												<tr>
													<th>App Type</th>
													<td><c:if
															test="${merchantBusinessFormBean.appType eq 0}">None</c:if>
														<c:if test="${merchantBusinessFormBean.appType eq 1}">Integrated</c:if>
														<c:if test="${merchantBusinessFormBean.appType eq 2}">Intent</c:if>
													</td>
												</tr>
												<tr>
													<th>Mosambee EMI</th>
													<td><c:if
															test="${merchantBusinessFormBean.mosambeeEmi eq 0}">No</c:if>
														<c:if test="${merchantBusinessFormBean.mosambeeEmi eq 1}">Merchant Level</c:if>
														<c:if test="${merchantBusinessFormBean.mosambeeEmi eq 2}">Terminal Level</c:if>
													</td>
												</tr>
												<tr>
													<th>Kiosk</th>
													<td><c:if
															test="${merchantBusinessFormBean.kiosk eq 0}">No</c:if> <c:if
															test="${merchantBusinessFormBean.kiosk eq 1}">Yes</c:if>
													</td>
												</tr>
												<tr>
													<th>Monthly Limit</th>
													<td>${merchantBusinessFormBean.monthlyLimit}</td>
												</tr>
												<tr>
													<th>Offline Transaction</th>
													<td><c:if
															test="${merchantBusinessFormBean.offlineTransaction eq 0}">No</c:if>
														<c:if
															test="${merchantBusinessFormBean.offlineTransaction eq 1}">Yes</c:if>
														<c:if
															test="${merchantBusinessFormBean.offlineTransaction eq 2}">Both</c:if>
													</td>
												</tr>

												<tr>
													<th>Merchant Registration Mode</th>
													<td><c:if
															test="${merchantBusinessFormBean.merchantRegistrationMode eq 0}">Offline</c:if>
														<c:if
															test="${merchantBusinessFormBean.merchantRegistrationMode eq 1}">Online (Web Instant)</c:if>
													</td>
												</tr>
												<tr>
													<th>Merchant MID TID Type</th>
													<td><c:if
															test="${merchantBusinessFormBean.merchantMidTidType eq 0}">Acquiring Bank MID and TID</c:if>
														<c:if
															test="${merchantBusinessFormBean.merchantMidTidType eq 1}">Aggregator MID and TID</c:if>
														<c:if
															test="${merchantBusinessFormBean.merchantMidTidType eq 2}">Instant (temp) MID and TID</c:if>
													</td>
												</tr>
												<tr>
													<th>Temp Merchant Code</th>
													<td>${ merchantBusinessFormBean.tempMerchantCode }</td>
												</tr>
												<tr>
													<th>Supervisor Check :</th>
													<td><c:if
															test="${merchantBusinessFormBean.isSupervisor eq 0}">No</c:if>
														<c:if test="${merchantBusinessFormBean.isSupervisor eq 1}">Yes</c:if>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>

								</td>
								</tr>
								</tbody>
								</table>


								</td>
								</tr>
								</tbody>
								</table>
							</div>
						</div>
					</div>
					<table id="enquiry-table-id"
						class="display table table-bordered table-sm mx-auto w-100">
						<thead class="whitesmoke">
							<tr>
								<th>Card Type</th>
								<th>Acquirer</th>
								<th>Tg</th>
								<th>MerchantCode</th>
								<th>Retailer Region</th>
								<th>City Code</th>
								<th>State Code</th>
								<th>Country Code</th>
								<th>Is Emi Allowed</th>
								<th>Mat Active Flag</th>
								<th>Khata</th>
							</tr>
						</thead>
						<tbody>
							<c:forEach items="${ integrationBeanList }" var="integration">
								<tr>
									<td>${ integration.getCardType() }</td>
									<td>${ integration.getAcquirer() }</td>
									<td>${ integration.getTg() }</td>
									<td>${ integration.getMerchantCode() }</td>
									<td>${ integration.getRetailerRegion() }</td>
									<td>${ integration.getCityCode() }</td>
									<td>${ integration.getStateCode() }</td>
									<td>${ integration.getCountryCode() }</td>
									<td>${ integration.getIsEmiAllowedText() }</td>
									<td>${ integration.getMatActiveFlagText() }</td>
									<c:if test="${ integration.getCardType() eq 'CARD'}">
										<td>${ integration.getIsKhataAllowedText() }</td>
									</c:if>
									<c:if test="${ integration.getCardType() ne 'CARD'}">
										<td>NA</td>
									</c:if>
								</tr>
							</c:forEach>
						</tbody>
					</table>
					</tr>
					</table>
				</div>
			</div>
			<c:if test="${merchantBusinessFormBean.status eq  'A' }">
				<div id="deactMerchnt">
					<center>
						<input type="button" class="btn btn-primary" value="De-activate"
							id="deactivateMerchant">
					</center>
				</div>
			</c:if>
			<div id="deactiveMerchnt">
				<center>
					<input type="button" class="btn btn-primary" value="De-activate"
						id="deactivateMerchnt">
				</center>
			</div>
			<c:if test="${merchantBusinessFormBean.status eq  'P' }">
				<c:if
					test="${merchantBusinessFormBean.createdUserId ne loggedinUserId}">
					<c:if
						test="${merchantBusinessFormBean.updatedUserId ne loggedinUserId}">
						<div id="approveRejectMerchnt">
							<center>
								<input type="button" class="btn btn-primary changeStatus"
									value="Approve" id="approveMerchant"> <input
									type="button" class="btn btn-primary pl-4 pr-4 changeStatus"
									value="Reject" id="rejectMerchant">
							</center>
						</div>
					</c:if>
				</c:if>
			</c:if>
			<br> <br>
		</div>
	</div>


	<!-- end of table list view -->
	<!--  /.modal-for-View Button Listing Page  -->

	<!--  /.modal-for-settle transaction  -->
	<div class="modal fade" id="reject-merchant">
		<div class="modal-dialog modal-sm">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
				</div>
				<div class="modal-body mx-auto d-block ">
					<div id="settle-txn-msg"></div>
				</div>
				<div class="modal-footer border-0 mx-auto d-block mb-5">

					Comments : <input type="text" name="comment" id="comment">
					<div id="comment_error" class="validationAlert"></div>
					<div class="ml-4 mt-5">
						<button class="btn btn-primary ml-5" id="confirm">Confirm</button>
						<button type="button" class="btn btn-default" id="button1"
							data-dismiss="modal">Cancel</button>
					</div>
				</div>
			</div>
			<!--  /.modal-content  -->
		</div>
		<!-- /.modal-dialog-->
	</div>


	<!-- close modal -->



	<%@ include file="../fragments/footer-js.jsp"%>

	<script
		src="${pageContext.servletContext.contextPath}/resources/js/merchant/view-merchant.js"></script>
</body>

</html>