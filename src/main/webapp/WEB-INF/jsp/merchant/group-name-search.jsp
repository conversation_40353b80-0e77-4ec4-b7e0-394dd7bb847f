<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="ISO-8859-1">
<title>Program List</title>
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
<meta id="app_context_path" name="app_context_path"
	content="${pageContext.servletContext.contextPath}" />
<%@ include file="../fragments/header-css.jsp"%>
<%@ include file="../fragments/datatables-css.jsp"%>

<link
	href="${pageContext.servletContext.contextPath}/resources/css/bootstrap-datepicker-min.css"
	rel="stylesheet" />
<link rel="stylesheet"
	href="${pageContext.servletContext.contextPath}/resources/css/select.min.css">
	
<style>


#card-alerts3 {
	display: none;
}

</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed">

	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>

		<div class="content-wrapper">
			<div class="container">
					<c:if test="${ msg != null }">
					<c:choose>
						<c:when test="${msg eq true}">
							<div id="card-alerts"
								class="alert alert-success alert-dismissible fade show"
								role="alert">
								Status Updated Successfully
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
						</c:when>
						<c:otherwise>
							<div id="card-alerts"
								class="alert alert-danger alert-dismissible fade show"
								role="alert">
								Error occurred.
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
						</c:otherwise>
					</c:choose>
				</c:if>
					<div id="card-alerts3"
								class="alert alert-success alert-dismissible fade show"
								role="alert">
								
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
					<c:if test="${bulk_upload_format_error != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						No Data Available!
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				
				<div class="card" id="container-card">
					<div class="card-header">
						<div class="row">
							<div class="col-sm-6">
								<h6>Program List</h6>
							</div>
							<div class="col-sm-6">
						
									<button
										class="btn btn-primary dvfont float-right mr-2 mt-2 mt-sm-0"
										type="button" id="create">
										<i class="fa fa-plus" aria-hidden="true"></i> Add Program
									</button>

							
							
							</div>
						</div>
					</div>
					<div class="card-body">

						
						 <div class="row ">
							<input type="hidden" id="acquirerId" value="${existingAcqId}">
							  <div class="col-sm-4">
								<div class="form-group input-group-sm">
									<label for="acquirer">Acquirer*</label>
								
									<select path="acquirer" class="form-control select2 " id = "acqId" name = "acqId">
									 <option  value="-1" >All Acquirer</option>
									 <option  value="0" >Applicable for CSS / No Acquirer</option>
									 
									 	 
									<c:forEach items="${acquirers}" var="acq">
									 	<option value="${acq.getKey() }"
													<c:if test='${acq.getKey() eq existingAcqId}'>
													selected
								</c:if>>${acq.getValue() }</option>
												<%-- 	<option value="${acq.getId()">${acq.getName() }</option> --%>

											</c:forEach>
								
									    
									 </select>
									
									
								</div>
								
						
							</div>
							<!-- search button Start-->
							<div class="col-4">
								<br>
								<div class="d-flex">
									<button id="search" type="button" class="btn btn-md btn-primary mr-2 dvfont" title="Search">
										<em class="fa fa-search"> </em> Search
									</button>
									
										<form name="submitForm" method="GET" action="${pageContext.servletContext.contextPath}/merchant/download-programType-details">
						 	<button
							type="submit"
									id="download-bulk-upload-format" role="button"
									class="btn btn-primary text-white btn-md"> <em
									class="fas fa-download"></em> Download 
								</button> 
								
									
										<input type="hidden" name="acqId" id ="acquirer">
								
						
								
								</form>
										
									
								
									
								
								</div>
							
						
								<div class="d-flex mx-auto mt-1">
	                        
							
								</div>
							</div>
							</div>
							
							
							
						
							<!-- End of search button -->	
							
						</div>
					
						<!-- /search button -->

						<!-- Data Table Body -->
						<div class="card-body" id="first-search">
							<div class="table-responsive">


														<!-- Data Tables header -->
								<table id="table_id"
									class="display table table-bordered table-sm mx-auto w-100"
									aria-describedby="">
									<thead>
										<tr>
										<th id="f">Sr No.</th>
										<th id="f">Program Id</th>
											<th id="f">Program Name</th>
											<th id="f">Acquirer Id</th>
											<th id="f">Acquirer Name</th>
											<th id="t">Status</th>
										 	<th id="t">Edit Status</th>
											<th id="totalsale">Applicable for CSS</th>
											<th id="">Edit CSS Status</th>
										
										</tr>
									</thead>
								</table>
								<!-- Data Tables header End-->

							</div>
						</div>
					</div>
					<!-- Data Table Body End-->
				</div>
			</div>
		</div>
	</div>



		
		<div class="modal fade" id="userStatusModal1" role="dialog">
			<div class="modal-dialog modal-md">
				<div class="modal-content">

					<div class="modal-header border-0">

						<button type="button" class="close" data-dismiss="modal">&times;</button>

					</div>
					<div class="modal-body border-0 mx-auto d-block">
						<input type="hidden" id="userId" value="">
						<input type="hidden" id="userId1" value="">
							<input type="hidden" id="userId2" value="">
						<div class="form-group input-group-sm">
								
								
									<select  class="form-control select2 " id = "acqId1" name = "acqId1">
										 <option selected disabled value="null" >Select Acquirer</option>
									<c:forEach items="${acquirers}" var="acq">
									    <option value="${acq.getKey() }" > ${acq.getValue() } </option>
									 </c:forEach>
									 </select>
									
											<div id="acquirer_alert" class="validationAlert"></div>
								</div>
					</div>
					<div class="modal-footer border-0 mx-auto d-block mb-5">

					
						<button type="button" class="btn btn-primary" id="confirm">Assign</button>

					</div>
				</div>
			</div>
		</div>

		
		

	<%@ include file="../fragments/footer-js.jsp"%>
	<%@ include file="../fragments/datatables-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/merchant/group-name-search.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/bootstrap-datepicker-min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/select.min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/validator.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/moment.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/locale/de.js"></script>
</body>

</html>