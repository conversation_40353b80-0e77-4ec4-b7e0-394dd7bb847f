<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>

<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="ISO-8859-1">
<title>SPOC</title>
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
<meta id="app_context_path" name="app_context_path"
	content="${pageContext.servletContext.contextPath}" />
<%@ include file="../fragments/header-css.jsp"%>
<%@ include file="../fragments/datatables-css.jsp"%>
<link
	href="${pageContext.servletContext.contextPath}/resources/css/bootstrap-datepicker-min.css"
	rel="stylesheet" />
<link rel="stylesheet"
	href="${pageContext.servletContext.contextPath}/resources/css/select.min.css">
</head>

<body class="hold-transition sidebar-mini layout-fixed">
	<div class="wrapper">
		<%@include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>

		<div class="content-wrapper">
		
		
			<div class="container">
				<c:if test="${ msg != null }">
					<div id="card-alerts" class="alert alert-danger alert-dismissible fade show" role="alert">
						No Data available.
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>

		
				<div class="card" id="container-card">
					<div class="card-header">
						<h6>SPOC Audit Logs</h6>
					</div>
					<input type="hidden" id="mmut">
					<form action="${pageContext.servletContext.contextPath}/auditlogs-reporting/spocdownload" id="cpocform" name="f" method="post">
						<div class="card-body">

							<div class="row">
								<div class="col-sm-3">
									<div class="form-group input-group-sm">
										<label for="userName">User Name</label>
										<input type="text" class="form-control no-special-char"
											id="userName"  placeholder="userName" name="userName"
											maxlength="100" autocomplete="off" />
											<div id="username_error" class="validationAlert"></div>

									</div>
								</div>
								
								<div class="col-sm-3">
									<div class="form-group input-group-sm">
										<label for="status">Status</label>
										<select class="form-control" id="status" name="status">
											
											<option value="">All</option>
											<option value="3">Block Users</option>
										</select>
										<div id="status_error" class="validationAlert"></div>
									</div>
								</div>
								<div class="col-sm-3">
								<div class="form-group mr-sm-4 ">
											<label class="mr-2">From Date </label>
											<div class="input-group input-group-sm">
												<div class="input-group-prepend">
													<span class="input-group-text"> <em
														class="far fa-calendar-alt"></em>
													</span>
												</div>
												<input type="text" class="form-control float-right"
													id="fromdate" name="fromDate" value="${fromDate}"
													placeholder="DD/MM/YYYY" autocomplete="off">
											</div>
											<div id="fromdate_error" class="validationAlert"></div>
											<div id="30days_error" class="validationAlert"></div>

											<!-- /input group -->
										</div>
								</div>
								<div class="col-sm-3">
								<div class="form-group">
											<label class="mr-2"> To Date</label>&nbsp;
											<div class="input-group input-group-sm">
												<div class="input-group-prepend">
													<span class="input-group-text"> <em
														class="far fa-calendar-alt"></em>
													</span>
												</div>
												<input type="text" class="form-control float-right"
													name="toDate" id="todate" value="${toDate}"
													placeholder="DD/MM/YYYY" autocomplete="off">
											</div>
											<!-- /input group -->
											<div id="todate_error" class="validationAlert"></div>
											<div id="exceeddays_error" class="validationAlert"></div>
										</div>								</div>
							</div>

														<div class="card-footer pb-0  mt-2 bg-white">
								<div class="row">
									<div class="d-flex mx-auto">
										<button id="search" type="button" class="btn btn-md btn-primary mr-2 dvfont"
											title="Search">
											<em class="fa fa-search"> </em> Search
										</button>
										<button  class="btn btn-primary mr-2 dvfont" type="button" id="download">
										<i class="fa fa-download" ></i> Export excel
									</button>
									</div>
								</div>
							</div>

						</div>


						<!-- Listing Here -->

						<div class="card-body pt-0 pb-2" id="cpos_table">
							<div class="table-responsive mt-4">
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}">

								<table id="cpos-table-id"
									class="display table table-bordered table-sm mx-auto w-100"
									aria-describedby="">
									<thead>
										<tr>
											<th id="srNo">Sr No</th>
											<th id="unm">User Name-Device Name</th>
											<th id="fc"> Failed Count</th>
											
											<th id="action">Reset</th>
											<th id="timer">Timer</th>
											<th id="ids">Rootedor Jail Broken</th>
											<th id="seboo">Secure Booting</th>
											<th id="selinux">SE Linux</th>
											<th id="bankRef">Debugging</th>
											<th id="cardBin">SuExists</th>
											<th id="isE">Is Emulator</th>
											<th id="sepal">Security Patch Level </th>
											<th id="accM"> Accessibility Mode</th>
											<th id="talkbe">Talkback Enabled </th>
											<th id="sre"> Screen Recording Enabled</th>
											<th id="rss">Remote Screen Sharing </th>
											<th id="prsc">Process cpoc </th>
											<th id="ed">Error Discription </th>
											
											</tr>

									</thead>
								</table>
							</div>
						</div>
					
					
				<!-- Modal -->
<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header border-0">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <input type="hidden" id="scpocId" name="scpocId" value="">
         <input type="hidden" id="insertionTime" name="insertionTime" value="">
      </div>
      <div class="modal-body border-0">
      	<div id="save-alert" class="richa" role="alert">
										<button type="button" class="close" data-dismiss="alert"
											aria-label="Close">
											<span aria-hidden="true">&times;</span>
										</button>
									</div>
        <h5 class="text-center">Are you sure you want to Unblocked ?</h5>
      </div>
      <div class="modal-footer border-0 mx-auto">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="yes">Yes</button>
      </div>
    </div>
  </div>
</div>
					
					
					</form>
				</div>

			</div>

		</div>
	</div>
	<%@ include file="../fragments/footer-js.jsp"%>
	<%@ include file="../fragments/datatables-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/spoc/spoc.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/bootstrap-datepicker-min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/select.min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/validator.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/moment.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/locale/de.js"></script>
		<script
		src="${pageContext.servletContext.contextPath}/resources/js/countdown.min.js"></script>
		<script
		src="${pageContext.servletContext.contextPath}/resources/js/fliptimer.js"></script>

</body>

</html>