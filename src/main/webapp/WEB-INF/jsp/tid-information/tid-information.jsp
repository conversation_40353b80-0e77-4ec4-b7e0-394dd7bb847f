<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="ISO-8859-1">
<title>Download Tid Information</title>
<%@ include file="../fragments/header-css.jsp"%>
<link
	href="${pageContext.servletContext.contextPath}/resources/css/bootstrap-datepicker-min.css"
	rel="stylesheet" />
<link rel="stylesheet"
	href="${pageContext.servletContext.contextPath}/resources/css/select.min.css">
	
		<style>

 #card-alerts2 {
	display: none;
}
 

</style>

</head>

<body class="hold-transition sidebar-mini layout-fixed">
	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>
		<div class="content-wrapper">
			<div class="container-fluid">
			

			<c:if test="${ invalid != null }">
				<div id="card-alerts"
					class="alert alert-danger alert-dismissible fade show"
					role="alert">
					No Data Available
					<button type="button" class="close" data-dismiss="alert"
						aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
			</c:if>
						

		<input type="hidden" id="baseUrl"  value='${baseUrl}'/>
		


				<div class="card" id="container-card">
				
					
				
				
					<div class="card-header">
						<div class="row">

							<div class="col">
								<h6 class="mb-0">Download Tid Information</h6>
							</div>
							
							
						
				
							<div class="col-auto">
						 		<ol class="breadcrumb float-sm-right bg-white mb-0 pl-0"
									id="breadcrumb">
									<li class="breadcrumb-item"><a
										href="${pageContext.servletContext.contextPath}/tg/download-common"><i
											class="fa fa-backward mr-1" aria-hidden="true"></i>Common
											Download</a></li>

								</ol>


							</div>
						</div>
					</div>
					
					
	
					<div class="card-body">
						<form
						action="${pageContext.servletContext.contextPath}/tidReport/download"
						id="downloadCommissionAPI" method="post">
						<input type="hidden" name="${_csrf.parameterName}"
							value="${_csrf.token}">
							
					
						
							<div class="row ">
							
							

								<div class="col-12 col-sm-6 col-md-3 my-2">
									<label>From Date <span class="text-danger"> *</span></label>
									<div class="input-group input-group-sm">
										<div class="input-group-prepend">
											<span class="input-group-text"> <em
												class="far fa-calendar-alt"></em></span>
										</div>
										<input type="text" name = "fromDate" class="form-control" id="fromdate"
											 placeholder="DD/MM/YYYY" autocomplete="off" />
									</div>
									<div id="fromdate_error" class="validationAlert"></div>
									<div id="30days_error" class="validationAlert"></div>
								</div>
								<div class="col-12 col-sm-6 col-md-3 my-2">
									<label>To Date <span class="text-danger"> *</span></label>
									<div class="input-group input-group-sm">
										<div class="input-group-prepend">
											<span class="input-group-text"> <em
												class="far fa-calendar-alt"></em></span>
										</div>
										<input type="text" name = "toDate" class="form-control" id="todate"
											 placeholder="DD/MM/YYYY" autocomplete="off" />
									</div>
									<div id="todate_error" class="validationAlert"></div>
									<div id="exceeddays_error" class="validationAlert"></div>
								</div>

								<div class="col-12 col-sm-6 col-md-3 my-2 ">
								<div class="form-group input-group-sm">
									<label> Acquirer<span class="text-danger"> *</span></label> 
									<select
										class="form-control select2" id="acquirer"
										name="acqId">
									
										
								<c:forEach items="${acquirers}" var="acq">
								    <option value="${acq.getKey() }" > ${acq.getValue() } </option>
								 </c:forEach>

									</select>
									<div id="acquirer_error" class="validationAlert"></div>
								</div>
							</div>

							<div class="col-12 col-sm-6 col-md-3 my-2">
							
							
							
							<label for="status">Status <span class="text-danger"> *</span> </label> <select id="status"
					name="status" class="form-control select2" >
							<option value="" selected="selected">Select Status</option>
				 	<option value="A" >Active</option>
					<option value="B">Block</option>
					<option value="D">Deactive</option>
					<option value="H">Hold</option>
					<option value="K">Key Sync</option>
					<option value="P">Pending</option>
					<option value="R">Reject</option>
				
					
				
					
				</select>
				<div id="status_error" class="validationAlert"></div>
				</div>

							
							</div>
							
						<div class = "row">
						
						<div class="col-12 col-sm-6 col-md-3 my-2">
						<label>MID</label>
						<div class="input-group input-group-sm">

							<input name = "mid" type="text" id="mid" class="form-control"
								placeholder="MID"  value=""
								autocomplete="off" />
						</div>
						<div id="mid_error" class="validationAlert"></div>
					</div>
					
					<div class="col-12 col-sm-6 col-md-3 my-2">
					<label>TID</label>
					<div class="input-group input-group-sm">

						<input name = "tid" type="text" id="tid" class="form-control"
							placeholder="TID"  value=""
							autocomplete="off" />
					</div>
					<div id="tid_error" class="validationAlert"></div>
				</div>
					<div class="col-12 col-sm-6 col-md-3 my-2">
					<label>User Name</label>
					<div class="input-group input-group-sm">

						<input name ="userName" type="text" id="userName" class="form-control"
							placeholder="User Name"  value=""
							autocomplete="off" />
					</div>
					<div id="userNumber_error" class="validationAlert"></div>
				</div>
				<div class="col-12 col-sm-6 col-md-3 my-2">
				<label>VPA</label>
				<div class="input-group input-group-sm">

					<input name = "vpa" type="text" id="vpa" class="form-control"
						placeholder="VPA"  value=""
						autocomplete="off" />
				</div>
				
			</div>
			
				<div class="col-12 col-sm-6 col-md-3 my-2">
							
							
							
							<label for="dukptFlag">DUKPT Flag  </label> <select id="dukptFlag"
					name="dukptFlag" class="form-control select2" >
					<option value=""> Select DUKPT </option>
				 	<c:forEach items="${dukpt}" var="dukpt">
								  
								   <option value="${dukpt.value}">
        ${dukpt.key}
    </option>
    
								 </c:forEach>
				
					
				
					
				</select>
		<!-- 		<div id="status_error" class="validationAlert"></div> -->
				</div>
				
					<div class="col-12 col-sm-6 col-md-3 my-2">
							
							
							
							<label for="status">Onboarding Type  </label> <select id="onboardingType"
					name="onboardingType" class="form-control select2" >
					<option value="-1" >All</option>
				 	<option value="0" >Upload</option>
					<option value="1">Form</option>
					<option value="2">Api</option>
				
				
					
				
					
				</select>
			<!-- 	<div id="status_error" class="validationAlert"></div> -->
				</div>
				

						</div>
					
					</div>


					<div class="row mb-3">
						<div class="mx-auto d-block">
							<button type="button"
								class="btn btn-primary dvfont mx-auto d-block" value="Submit"
								id="downloadAPI">
								<i class="fa fa-download" aria-hidden="true"></i> Download
							</button>
						</div>
					</div>
		
			
			</div>

					

						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
	
		
		
	<%@ include file="../fragments/footer-js.jsp"%>
	<script
	src="${pageContext.servletContext.contextPath}/resources/js/tidInformation/tid-information.js"></script>	<script
		src="${pageContext.servletContext.contextPath}/resources/js/bootstrap-datepicker-min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/select.min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/validator.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/moment.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/locale/de.js"></script>
</body>
</html>
