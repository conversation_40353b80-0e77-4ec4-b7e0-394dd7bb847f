<%@page import="com.mosambee.bean.CustomUser"%>
<%@page import="org.springframework.security.core.context.SecurityContextHolder"%>
<nav class="main-header navbar navbar-expand navbar-dark"style="
    position: sticky;
    top: 0px;
">
    <!-- Left navbar links -->
    <ul class="navbar-nav mr-auto">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#"><em class="fas fa-bars"></em></a>
        </li>
    </ul>
    <ul class="navbar-nav">
        <li class="nav-item">
            <a href="#" class="nav-link" onclick="document.getElementById('logout-form').submit();">
                <%= ((CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal()).getMyMap().get("firstName") %> &nbsp;<em
                    class="fas fa-sign-out-alt"></em>       
                     logout</a>
        </li>
        
    </ul>
    

</nav>
<input type="hidden" id="myRole" value="<%= ((CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal()).getMyMap().get("role") %>" />
<form id="logout-form" method="POST" action="${pageContext.servletContext.contextPath}/logout">
    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}">
</form>