<aside class="main-sidebar sidebar-dark-primary elevation-4 "
	id="sidebar">
	<a href="${pageContext.servletContext.contextPath}/" class="brand-link">
		<img
		src="${pageContext.servletContext.contextPath}/resources/images/logo.png"
		alt="brand logo" class="brand-image img-circle elevation-3"
		style="opacity: .8"> <span class="brand-text font-weight-light">VAS</span>
	</a>
	<div class="sidebar">
		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a href="#" id="merchant"
					class="nav-link"> <em class="nav-icon fas fa-indent"></em>
						<p class="font-weight-bold">
							Merchant<em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN','ROLE_SITE_USER')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/merchant/"
								class="nav-link"> <em class="nav-icon fa fa-eye"></em>
									<p>Active Merchant</p>
							</a></li>
						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/merchant/add-merchant"
								class="nav-link"> <em class="nav-icon fa fa-plus-square"></em>
									<p>Add Merchant</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/merchant/pending"
								class="nav-link" id="pendingMerchant"> <em
									class="nav-icon fa fa-eye"></em>
									<p>Pending Merchant</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/merchant/rejected"
								class="nav-link"> <em class="nav-icon fa fa-eye"></em>
									<p>Rejected Merchant</p>
							</a></li>
						</security:authorize>




						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/merchant/incomplete-merchants"
								class="nav-link"> <em class="nav-icon fa fa-eye"></em>
									<p>Incomplete Merchant</p>
							</a></li>
						</security:authorize> --%>

						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/merchant/upload-merchant"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Merchant Upload</p>
							</a></li>
						</security:authorize> --%>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/bqr-merchants/add-bqr-merchants"
								class="nav-link"> <em class="far fa-plus-square nav-icon"></em>
									<p>Add BQR Merchants</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/bqr-merchants/bqr-merchants-list"
								class="nav-link"> <em class="far fa-plus-square nav-icon"></em>
									<p>List BQR Merchants</p>
							</a></li>
						</security:authorize>
						<%-- 		<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/bqr-merchants/upload-bqr-view"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Upload BQR Merchants</p>
							</a></li>
						</security:authorize> --%>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/offlinemerchants/offline-merchants-list"
								class="nav-link"> <em class="fa fa-list-ul nav-icon"></em>
									<p>Offline Merchant</p>
							</a></li>
						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/non-card-merchants/search"
								class="nav-link"> <em class="fa fa-address-card nav-icon"></em>
									<p>Non Card Merchant Download</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/terms-and-conditons/add-terms"
								class="nav-link"> <em class="nav-icon fa fa-plus-square"></em>
									<p>Add Terms And Conditions</p>
							</a></li>
						</security:authorize>

						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/upi-sync/upload-upi-sync"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>UPI Sync</p>
							</a></li>
						</security:authorize> --%>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item" id="termslist1"><a id="termslist2"
								href="${pageContext.servletContext.contextPath}/terms-and-conditons/list-terms"
								class="nav-link"> <em class="nav-icon fa fa-plus-square"></em>
									<p>Terms And Conditions List</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item" id="termslist1"><a
								id="tempMidTidDownload"
								href="${pageContext.servletContext.contextPath}/tempMidTid/data-download"
								class="nav-link"> <em class="nav-icon fa fa-plus-square"></em>
									<p>Instant Registration Download</p> <!-- Temp MID/TID download-->
							</a></li>
						</security:authorize>
						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item" id="termslist1"><a
								id="tempMidTidUpload"
								href="${pageContext.servletContext.contextPath}/tempMidTid/data-upload-view"
								class="nav-link"> <em class="nav-icon fa fa-plus-square"></em>
									<p>Instant Registration Upload</p> <!-- Temp MID/TID upload-->
							</a></li>
						</security:authorize> --%>
						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item" id="termslist1"><a id="smsPayUpload"
								href="${pageContext.servletContext.contextPath}/smsPay/data-upload-view"
								class="nav-link"> <em class="nav-icon fa fa-plus-square"></em>
									<p>SMS Pay Upload</p>
							</a></li>
						</security:authorize> --%>

						<%--  	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/wallet-upload/wallet-upload-view"
								class="nav-link"> <em class="nav-icon fa fa-upload"></em>
									<p>Wallet Upload</p>
							</a></li>
						</security:authorize> --%>
						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/offlinemerchants/upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Offline Key Upload</p>
							</a></li>
						</security:authorize>  --%>
						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/midUpload/upload-view"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>MID Upload</p>
							</a></li>
						</security:authorize> --%>

						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/merchant-status/merchant-status-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Merchant Status Upload</p>
							</a></li>
						</security:authorize> --%>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/mobile-user/mobile-view"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Default User Password</p>
							</a></li>
						</security:authorize>


						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/merchant/group-name-search"
								class="nav-link"> <em class="fa fa-search nav-icon"></em>
									<p>List Group Search</p>
							</a></li>
						</security:authorize>


						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a id="merchantInfo"
								href="${pageContext.servletContext.contextPath}/merchantInfo/basicInfo"
								class="nav-link"> <em class="nav-icon fa fa-plus-square"></em>
									<p>Merchant Information</p>
							</a></li>
						</security:authorize>



						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item has-treeview"><a id="cpoctab" href="#"
								class="nav-link"> <em class="fa fa-list-ul nav-icon"></em>
									<p>CPOC</p> <em class="right fas fa-angle-left"></em>
							</a>
								<ul class="nav nav-treeview">
									<security:authorize
										access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
										<li class="nav-item"><a
											href="${pageContext.servletContext.contextPath}/auditlogs-reporting/cpoc"
											class="nav-link">
												<p class="ml-5">
													CPOC Listing</em>
												</p>
										</a></li>
									</security:authorize>


									<security:authorize
										access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
										<li class="nav-item"><a
											href="${pageContext.servletContext.contextPath}/auditlogs-reporting/device-binding"
											class="nav-link">
												<p class="ml-5">
													Device binding</em>
												</p>
										</a></li>
									</security:authorize>
									<security:authorize
										access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
										<li class="nav-item"><a
											href="${pageContext.servletContext.contextPath}/auditlogs-reporting/blacklist-device"
											class="nav-link">
												<p class="ml-5">
													BlackList Device</em>
												</p>
										</a></li>
									</security:authorize>
									<security:authorize
										access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
										<li class="nav-item"><a
											href="${pageContext.servletContext.contextPath}/auditlogs-reporting/blacklist-device-list"
											class="nav-link">
												<p class="ml-5">
													BlackListed Device List</em>
												</p>
										</a></li>
									</security:authorize>

								</ul></li>
						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/auditlogs-reporting/spoc"
								class="nav-link"> <em class="fa fa-list-ul nav-icon"></em>
									<p>SPOC</p>
							</a></li>
						</security:authorize>


						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/deactivated_posId/deactivated_posId_upload"
								class="nav-link"> <em class="fa fa-list-ul nav-icon"></em>
									<p>Deactivated PosId Upload</p>
							</a></li>
						</security:authorize> --%>




					</ul>
		</nav>
		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="terminal-user"
					href="#" class="nav-link"> <em class="nav-icon fas fa-indent"></em>
						<p class="font-weight-bold">
							Terminal Users<em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/pending-users/list"
								class="nav-link"> <em class="nav-icon fa fa-user-plus"></em>
									<p>Pending Users</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/pending-users/rejected-users"
								class="nav-link"> <em class="nav-icon fa fa-user-times"></em>
									<p>Rejected Users</p>
							</a></li>
						</security:authorize>

						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/terminal-users/terminal-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Terminal User Upload</p>
							</a></li>
						</security:authorize> --%>
						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/terminal-approve/terminal-approve-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Terminal Approve Upload</p>
							</a></li>
						</security:authorize> --%>
						<%-- 		<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/amex-users/amex-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>AMEX User Upload</p>
							</a></li>
						</security:authorize> --%>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/amex/terminal-download"
								class="nav-link"> <em class="fa fa-download nav-icon"></em>
									<p>AMEX User Download</p>
							</a></li>
						</security:authorize>


						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/ter-details/getdetails"
								class="nav-link"> <em class="fa fa-download nav-icon"></em>
									<p>Terminal User Download</p>
							</a></li>
						</security:authorize>
						<li class="nav-item has-treeview value" id="bankdetailmenu"><a
							id="terminal-bank-detail" href="#" class="nav-link"> <em
								class="fas fa-user-shield nav-icon"></em>
								<p class="font-weight-bold">
									Bank Detail<em class="right fas fa-angle-left"></em>
								</p>
						</a>
							<ul class="nav nav-treeview">
								<%-- 		<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/bank-detail/bank-upload"
										class="nav-link"> <!-- 								<em class="fa fa-plus-square nav-icon"></em>
 -->
											<p class="ml-5">
												Upload Bank Detail</em>
											</p>
									</a></li>
								</security:authorize> --%>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_USER','ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item "><a
										href="${pageContext.servletContext.contextPath}/bank-detail/bank-mapping-view"
										class="nav-link"> <!-- <em class="nav-icon fa fa-file"></em>
 -->
											<p class="ml-5">
												Search Bank Detail </em>
											</p>
									</a></li>
								</security:authorize>
							</ul> <%--  <security:authorize
								access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
								<li class="nav-item"><a
									href="${pageContext.servletContext.contextPath}/micro-atm/micro-upload"
									class="nav-link"> <em class="nav-icon fa fa-user-plus"></em>
										<p>Micro ATM Upload</p>
								</a></li>
							</security:authorize> --%></li>

						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/terminal-merchant/status-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Merchant/TID Deactivation</p>
							</a></li>
						</security:authorize> --%></li>




				<%-- 	<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/aeps/aeps-upload"
						class="nav-link"> <em class="nav-icon fa fa-upload"></em>
							<p>Aeps Upload</p>
					</a></li>
				</security:authorize> --%>

				<%-- 	<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/equitas/equitas-upload"
						class="nav-link"> <em class="nav-icon fa fa-upload"></em>
							<p>Equitas/Kotak Upload</p>
					</a></li>
				</security:authorize> --%>

				<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/IMS/download-IMS"
						class="nav-link"> <em class="nav-icon fa fa-download"></em>
							<p>IMS Download</p>
				</security:authorize>
				<%-- <security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/ims/ims-upload"
						class="nav-link"> <em class="nav-icon fa fa-upload"></em>
							<p>IMS Posting</p>
					</a></li>
				</security:authorize> --%>

				<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/tid-information/tid-information-view"
						class="nav-link"> <em class="nav-icon fa fa-upload"></em>
							<p>TID Information</p>
					</a></li>
				</security:authorize>
				<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/user/users-under-settlement"
						class="nav-link"> <em class="fa fa-ban nav-icon"></em>
							<p>Users Under Settlement</p>
					</a></li>

				</security:authorize>

				<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/terminal-users/download-Device"
						class="nav-link"> <em class="fa fa-tablet nav-icon"></em>
							<p>Acquirer Binding</p>
					</a></li>

				</security:authorize>


				<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/terminal-users/userBinding-view



"
						class="nav-link"> <em class="nav-icon fa fa-user-plus"></em>
							<p>User Binding</p>
					</a></li>

				</security:authorize>

				<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN', 'ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/terminal-users/bpBankFile-view"
						class="nav-link"> <em class="fa fa-download nav-icon"></em>
							<p>Bharat Pe File Download</p>
					</a></li>
				</security:authorize>

				<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN', 'ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/keyInjection/keyInjection-view"
						class="nav-link"> <em class="fa fa-download nav-icon"></em>
							<p>KeyInjection File Download</p>
					</a></li>
				</security:authorize>

				<security:authorize
					access="hasAnyRole('ROLE_SITE_ADMIN', 'ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
					<li class="nav-item"><a
						href="${pageContext.servletContext.contextPath}/supervisor/supervisorView"
						class="nav-link"> <em class="fa fa-download nav-icon"></em>
							<p>Supervisor</p>
					</a></li>
				</security:authorize>
			</ul>
			</li>
			</ul>
		</nav>






		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a href="#" id="emi"
					class="nav-link"> <em class="nav-icon fas fa-indent"></em>
						<p class="font-weight-bold">
							EMI<em class="right fas fa-angle-left"></em>
						</p>
				</a>
					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/enquiry-reporting/enquiry"
								class="nav-link"> <em class="nav-icon fa fa-file"></em>
									<p>Enquiry Report</p>
							</a></li>
						</security:authorize>

						<!-- Merchant Group Start-->

						<li class="nav-item has-treeview"><a id="merchant-group"
							href="#" class="nav-link"> <em
								class="fas fa-user-shield nav-icon"></em>
								<p class="font-weight-bold ml-3">
									Report Configuration<em class="right fas fa-angle-left"></em>
								</p>
						</a>

							<ul class="nav nav-treeview" id="merchant">

								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/emi-issuer/add-issuer-tid"
										class="nav-link ">
											<p class="ml-5">

												Add TID Configuration</em>
											</p>
									</a></li>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_USER','ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/emi-issuer/list-issuer-tid"
										class="nav-link">
											<p class="ml-5">
												Report TID Configuration</em>
											</p>
									</a></li>
							</ul></li>


						<!-- Merchant Group END-->

						<li class="nav-item has-treeview " id="emi-configurationmenu">
							<a id="emi-configuration" href="#" class="nav-link"> <em
								class="fas fa-user-shield nav-icon"></em>
								<p class="font-weight-bold ml-3">
									EMI Configuration<em class="right fas fa-angle-left"></em>
								</p>
						</a>

							<ul class="nav nav-treeview" id="emitab">
								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/group-merchant/create-merchant-group"
										class="nav-link ">
											<p class="ml-5">
												Create Merchant Group</em>
											</p>
									</a></li>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_USER','ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/group-merchant/list-merchant-group"
										class="nav-link">
											<p class="ml-5">
												List Merchant Group</em>
											</p>
									</a></li>
								</security:authorize>
								<%-- <security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/emi-bulk-upload/mid-tid-upload"
										class="nav-link">
											<p class="ml-5">Upload Tid/DCEMI</p>
									</a></li>
								</security:authorize> --%>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN', 'ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
									<li class="shubham nav-item"><a
										href="${pageContext.servletContext.contextPath}/emi-bulk-upload/emi-search"
										class="shubham nav-link ">
											<p class="ml-5">List Tid/DCEMI</p>
									</a></li>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/subvention/add-subvention-rule"
										class="nav-link">
											<p class="ml-5">Add Subvention Rule</p>
									</a></li>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/subvention/list-subvention-rule"
										class="nav-link" id="listSubvention">
											<p class="ml-5">List of Subvention Rules</p>
									</a></li>
								</security:authorize>
								<%-- <security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/subvention/upload-subvention-rule-view"
										class="nav-link">
											<p class="ml-5">
												Upload Subvention Rule Group</em>
											</p>
									</a></li>
								</security:authorize> --%>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/subvention/add-subvention-offer"
										class="nav-link">
											<p class="ml-5">Add Subvention Offer</p>
									</a></li>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/subvention/list-subvention-offer"
										class="nav-link">
											<p class="ml-5">List of Subvention Offer</p>
									</a></li>
								</security:authorize>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/emi-issuer/add-issuer-details"
										class="nav-link">
											<p class="ml-5">
												Add EMI-Issuer</em>
											</p>
									</a></li>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_USER','ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/emi-issuer/list-issuer-emi"
										class="nav-link">
											<p class="ml-5">
												List EMI-Issuer</em>
											</p>
									</a></li>
								</security:authorize>



							</ul>
						</li>



						<!-- 				acquirer-issuer -->

						<li class="nav-item has-treeview "><a id="acquirer-issuer"
							href="#" class="nav-link"> <em
								class="fas fa-user-shield nav-icon"></em>
								<p class="font-weight-bold ml-3">
									Acquirer-Configuration<em class="right fas fa-angle-left"></em>
								</p>
						</a>

							<ul class="nav nav-treeview" id="acquirer-config">

								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/acquirer-issuer/add-acquirer-issuer"
										class="nav-link">
											<p class="ml-5">
												Add Issuer</em>
											</p>
									</a></li>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_USER','ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/acquirer-issuer/list-acquirer-issuer"
										class="nav-link">
											<p class="ml-5">
												List Issuer</em>
											</p>
									</a></li>
								</security:authorize>

							</ul></li>


						<!--  end of acquirer issuer -->

						<!-- bin configuration -->
						<li class="nav-item has-treeview "><a
							id="detailsbin-configuration" href="#" class="nav-link"> <em
								class="fas fa-user-shield nav-icon"></em>
								<p class="font-weight-bold ml-3">
									Bin Configuration<em class="right fas fa-angle-left"></em>
								</p>
						</a>

							<ul class="nav nav-treeview" id="bin-config">

								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/detailsbin/add-new-bin"
										class="nav-link">
											<p class="ml-5">Add Bin</p>
									</a></li>
								</security:authorize>

								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/detailsbin/list"
										class="nav-link">
											<p class="ml-5">List Bin</p>
									</a></li>
								</security:authorize>


								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/detailsbin/add-issuer-mcc"
										class="nav-link">
											<p class="ml-5">
												Block Bin (MCC)</em>
											</p>
									</a></li>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_USER','ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/detailsbin/list-issuer-mcc"
										class="nav-link">
											<p class="ml-5">
												Blocked MCC List</em>
											</p>
									</a></li>
								</security:authorize>


							</ul></li>

						<!--end  bin configuration -->



						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/transaction/transaction-report-view"
								class="nav-link"> <em class="nav-icon fa fa-file"></em>
									<p>Transaction Report</p>
							</a></li>
						</security:authorize>
						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/emi-conversion/emi-conversion-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Conversion Upload</p>
							</a></li>
						</security:authorize> --%>

						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/emi/emi-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Emi Upload</p>
							</a></li>
						</security:authorize> --%>
						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/sbi-emi/add-sbi-emi-mid"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>ADD SBI EMI MID</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/sbi-emi/add-sbi-emi-tid"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>ADD SBI EMI TID</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/sbi-emi/sbi-emi-mid-tid-list"
								class="nav-link"> <em class="nav-icon fa fa-file"></em>
									<p>List of SBI EMI MID/TID</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/sbi-emi-upload/sbi-mid-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Upload SBI EMI MID</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/sbi-emi-tid-upload/sbi-tid-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Upload SBI EMI TID</p>
							</a></li>
						</security:authorize> --%>
						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/sbi-emi-status-upload/sbi-emi-status-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>SBI EMI STATUS UPLOAD</p>
							</a></li>
						</security:authorize> --%>

					</ul></li>
			</ul>
		</nav>
		<%-- 		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="payLater" href="#"
					class="nav-link"> <em class="nav-icon fa fa-qrcode"></em>
						<p class="font-weight-bold">
							PAYLATER <em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN','ROLE_SITE_USER')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/payLater/enquiry-emi"
								class="nav-link"> <em class=" fa fa-file nav-icon"></em>
									<p class="font-weight-bold">Enquiry</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN','ROLE_SITE_USER')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/paylater-transaction/transaction-emi"
								class="nav-link"> <em class=" fa fa-file nav-icon"></em>
									<p class="font-weight-bold">Transaction</p>
							</a></li>
						</security:authorize>


					</ul></li>
			</ul>
		</nav> --%>
		<nav class="mt-2" aria-label="Site-Menu">



			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a href="#" id="configure"
					class="nav-link"> <em class=" fab fa-connectdevelop nav-icon"></em>
						<p class="font-weight-bold">
							Configuration <em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/tg/upload-common"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Upload</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/tg/download-common"
								class="nav-link"> <em class="fa fa-download nav-icon"></em>
									<p>Download</p>
							</a></li>
						</security:authorize>
						<security:authorize access="hasAnyRole('ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/tg/tg-list"
								class="nav-link"> <em class="far fa-eye nav-icon"></em>
									<p>List tg</p>
							</a></li>
						</security:authorize>
						<security:authorize access="hasAnyRole('ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/tg/add-tg"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>Add tg</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/acquirer/acquirer-view"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Mat Code</p>
							</a></li>
						</security:authorize>
						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/notification/upload-view"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Notification Upload</p>
							</a></li>
						</security:authorize> --%>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/api-password-config/create-api-group"
								class="nav-link" id="createApi"> <em
									class="fa fa-plus-square nav-icon"></em>
									<p>Create API Division</p>
							</a></li>
						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/api-password-config/list-api-group"
								class="nav-link"> <em class="far fa-eye nav-icon"></em>
									<p>List of API Division</p>
							</a></li>
						</security:authorize>
						<%-- 				<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/api-password-config/mid-bulk-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Upload MID</p>
							</a></li>
						</security:authorize> --%>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/api-password/view"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Common API Password</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/networkmessages/networkmessages-list"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Network Messages</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/notification/add-notification"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Add Report Scheduler</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN','ROLE_SITE_USER')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/notification/notification-list"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Report Scheduler List</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN','ROLE_SITE_USER')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/send-notification/send-notification-view"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Forgot Password Email / Mobile Number</p>
							</a></li>
						</security:authorize>
						
							<security:authorize
							access="hasAnyRole('ROLE_SITE_USER')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/commissioning-api/upload-view"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Upload Commission API Data</p>
							</a></li>
						</security:authorize>


						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN','ROLE_SITE_USER')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/enterprise/blacklisted-bin-download"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Download Blacklist Data</p>
							</a></li>
						</security:authorize>


				

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN','ROLE_SITE_USER')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/commissioning-api/commission-download"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Download Commission API Data</p>
							</a></li>
						</security:authorize>

						<!-- bin configuration -->
						<li class="nav-item has-treeview "><a id="bindetails"
							href="#" class="nav-link"> <em
								class="fas fa-user-shield nav-icon"></em>
								<p class="font-weight-bold">
									Bin <em class="right fas fa-angle-left"></em>
								</p>
						</a>

							<ul class="nav nav-treeview" id="bin-category">

								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/binDetails/bin-category-list-view"
										class="nav-link">
											<p class="ml-4">Bin Category</p>
									</a></li>
								</security:authorize>


								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/binDetails/mat-bin-category-mapping-form-view?id=0"
										class="nav-link">
											<p class="ml-4">Add Mat Bin Category Mapping</p>
									</a></li>
								</security:authorize>


								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/binDetails/mat-bin-category-mapping-list-view"
										class="nav-link">
											<p class="ml-4">Mat Bin Category Mapping List</p>
									</a></li>
								</security:authorize>

								<%-- 			<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/detailsbin/list"
										class="nav-link">
											<p class="ml-5">List Bin</p>
									</a></li>
								</security:authorize>


								<security:authorize
									access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/detailsbin/add-issuer-mcc"
										class="nav-link">
											<p class="ml-5">
												Block Bin (MCC)</em>
											</p>
									</a></li>
								</security:authorize>
								<security:authorize
									access="hasAnyRole('ROLE_SITE_USER','ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
									<li class="nav-item"><a
										href="${pageContext.servletContext.contextPath}/detailsbin/list-issuer-mcc"
										class="nav-link">
											<p class="ml-5">
												Blocked MCC List</em>
											</p>
									</a></li>
								</security:authorize> --%>


							</ul></li>

						<!--end  bin configuration -->


						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN','ROLE_SITE_USER')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/amazon-vpa/amazon-vpa-view"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Amazon VPA</p>
							</a></li>
						</security:authorize> --%>

					</ul></li>

			</ul>
		</nav>

		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="insert-bqr" href="#"
					class="nav-link"> <em class="nav-icon fas fa-indent"></em>
						<p class="font-weight-bold">
							Transactions<em class="right fas fa-angle-left"></em>
						</p>
				</a>
					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a id="txn"
								href="${pageContext.servletContext.contextPath}/transaction/"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p class="">Transactions</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/searchbybillnumber/billnumber-transaction-list"
								class="nav-link"> <em class="fa fa-search nav-icon"></em>
									<p class="">Search by BillNumber</p>
							</a></li>
						</security:authorize>


						<%-- 			<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/upi-transactions/upload-upi-view"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p class="">Upload UPI Transactions</p>
							</a></li>
						</security:authorize> --%>

						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/bqr-transactions/bqr-upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p class="">BQR Transactions upload</p>
							</a></li>
						</security:authorize> --%>
					</ul></li>
			</ul>
		</nav>
		<security:authorize
			access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

			<nav class="mt-2" aria-label="Site-Menu">
				<ul class="nav nav-pills nav-sidebar flex-column"
					data-widget="treeview" role="menu" data-accordion="false">

					<li class="nav-item has-treeview "><a id="merspc" href="#"
						class="nav-link"> <em class="nav-icon fas fa-user"></em>
							<p class="font-weight-bold">
								Merchant Specific <em class="right fas fa-angle-left"></em>
							</p>
					</a>
						<ul class="nav nav-treeview">

							<security:authorize
								access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
								<li class="nav-item"><a
									href="${pageContext.servletContext.contextPath}/merchant-specific/merchant-mapping-view"
									class="nav-link"> <em class="fa fa-eye nav-icon"></em>
										<p>URL Mapping List</p>
								</a></li>
							</security:authorize>
							<%-- <security:authorize
								access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
								<li class="nav-item"><a
									href="${pageContext.servletContext.contextPath}/merchant-specific/bulk-upload"
									class="nav-link"> <em class="fa fa-upload nav-icon"></em>
										<p>Upload Mapping URL</p>
								</a></li>
							</security:authorize> --%>
							<security:authorize
								access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
								<li class="nav-item"><a
									href="${pageContext.servletContext.contextPath}/merchant-specific/merchant-key-view"
									class="nav-link"> <em class="fa fa-eye nav-icon"></em>
										<p>Merchant Key</p>
								</a></li>
							</security:authorize>

							<%-- 	<security:authorize
								access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
								<li class="nav-item"><a
									href="${pageContext.servletContext.contextPath}/merchant-specific/key-bulk-upload"
									class="nav-link"> <em class="fa fa-upload nav-icon"></em>
										<p>Upload Key</p>
								</a></li>
							</security:authorize> --%>
						</ul></li>
				</ul>
			</nav>
		</security:authorize>

		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a href="#" class="nav-link">
						<em class=" fab fa-connectdevelop nav-icon"></em>
						<p class="font-weight-bold">
							Enterprise <em class="right fas fa-angle-left"></em>
						</p>
				</a>
					<ul class="nav nav-treeview">

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN','ROLE_SITE_USER')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/enterprise/enterprise-list"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>Enterprise List</p>
							</a></li>
						</security:authorize>
						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/enterprise-upload/upload-view"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>Merchant Upload</p>
							</a></li>
						</security:authorize> --%>

						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/enterprise-parent-upload/view"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>Enterprise Bulk Upload</p>

							</a></li>
						</security:authorize> --%>
						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/enterprise-terminal/enterprise-list"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>Enterprise Terminal List</p>
							</a></li>
						</security:authorize> --%>
					</ul></li>
			</ul>
		</nav>
		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="user" href="#"
					class="nav-link"> <em class=" fa fa-users  nav-icon"></em>
						<p class="font-weight-bold">
							Users <em class="right fas fa-angle-left"></em>
						</p>
				</a>
					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/user/list"
								class="nav-link"> <em class="far fa-eye nav-icon"></em>
									<p>List</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/user/instant-user-list"
								class="nav-link"> <em class="far fa-eye nav-icon"></em>
									<p>Instant Blocked List</p>
							</a></li>

						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/user/create-user"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>Create User</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/user/web-blocked-users"
								class="nav-link"> <em class="fa fa-ban nav-icon"></em>
									<p>Web Blocked</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/user/mobile-blocked-users"
								class="nav-link"> <em class="fa fa-ban nav-icon"></em>
									<p>Mobile Blocked</p>
							</a></li>

						</security:authorize>

					</ul></li>
			</ul>
		</nav>
		<%-- 	<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a href="#" class="nav-link">
						<em class="fas fa-tasks  nav-icon"></em>
						<p class="font-weight-bold">
							Settlement <em class="right fas fa-angle-left"></em>
						</p>
				</a>
					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/settlement/initiate"
								class="nav-link"> <em class="far fa-plus-square nav-icon"></em>
									<p>Initiate</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/settlement/view-settlement-merchant"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>Merchant Settlement</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/settlement/view-user"
								class="nav-link"> <em class="fa fa-eye nav-icon"></em>
									<p>User Settlement Log</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/settlement/time"
								class="nav-link"> <em class="far fa-clock nav-icon"></em>
									<p>Time Settlement Log</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/settlement/auto"
								class="nav-link"> <em class="fas fa-robot nav-icon"></em>
									<p>Auto Settlement Log</p>
							</a></li>

						</security:authorize>
					</ul></li>
			</ul>

		</nav> --%>




		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="acquirer-user"
					href="#" class="nav-link"> <em
						class="fas fa-user-shield nav-icon"></em>
						<p class="font-weight-bold">
							Acquirers<em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/adminacquirer/admin-acquirer-view"
								class="nav-link"> <em class="far fa-eye nav-icon"></em>
									<p>Acquirer Information</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/adminacquirer/add-acquirer"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>Add Acquirer</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/adminacquirer/list-acquirer"
								class="nav-link"> <em class="far fa-eye nav-icon"></em>
									<p>List of Acquirers</p>
							</a></li>

						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/adminacquirer/acquirer-profile"
								class="nav-link"> <em class="far fa-eye nav-icon"></em>
									<p>Acquirer Profile Information</p>
							</a></li>

						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/acquirer-user/create-acquirer-user"
								class="nav-link"> <em class="nav-icon fa fa-user"></em>
									<p>
										Create Acquirer User</em>
									</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/acquirer-user/acquirer-user-list"
								class="nav-link"> <em class="nav-icon fa fa-list"></em>
									<p>
										List of Acquirer User</em>
									</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/acquirerLimits/list-acquirer-txn"
								class="nav-link"> <em class="nav-icon fa fa-list"></em>
									<p>
										Acquirer Transaction Limits</em>
									</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/acquirer-settlement/acquirer-settlement-view"
								class="nav-link"> <em class="nav-icon fa fa-list"></em>
									<p>
										Acquirer Settlement</em>
									</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/adminacquirer/add-transaction-posting"
								class="nav-link"> <em class="nav-icon fa fa-list"></em>
									<p>
										Add Transaction Posting URL</em>
									</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/adminacquirer/transaction-posting-view"
								class="nav-link"> <em class="nav-icon fa fa-list"></em>
									<p>
										List Transaction Posting URL</em>
									</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/rule-set/rule-set-view"
								class="nav-link"> <em class="nav-icon fa fa-list"></em>
									<p>
										Add Rule Set </em>
									</p>
							</a></li>
						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/rule-set/rule-set-list"
								class="nav-link"> <em class="nav-icon fa fa-list"></em>
									<p>
										Rule Set List </em>
									</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/sms-email-template/sms-email-template-view?acquirer=0"
								class="nav-link" method="post"> <em
									class="nav-icon fa fa-list"></em>
									<p>Acquirer SMS/E-mail Template</p>
							</a></li>
						</security:authorize>

					</ul></li>
			</ul>
		</nav>



		<%-- <nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="bin-detail" href="#"
					class="nav-link"> <em class="nav-icon fas fa-indent"></em>
						<p class="font-weight-bold">
							Bin Details<em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/bindetails/add-bin"
								class="nav-link"> <em class="far fa-plus-square nav-icon"></em>
									<p>Add Bin</p>
							</a></li>

						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/bindetails/bin-list"
								class="nav-link"> <em class="far fa-plus-square nav-icon"></em>
									<p>Bin List</p>
							</a></li>

						</security:authorize>

						 <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/bindetails/upload-bin"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Upload Bin</p>
							</a></li>

						</security:authorize>

					</ul></li>
			</ul>
		</nav>
 --%>
		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="insert-bqr" href="#"
					class="nav-link"> <em class="nav-icon fas fa-indent"></em>
						<p class="font-weight-bold">
							TSS<em class="right fas fa-angle-left"></em>
						</p>
				</a>
					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/tss/tss-view"
								class="nav-link"> <em
									class="far fa-arrow-alt-circle-right nav-icon"></em>
									<p>Perform TSS</p>
							</a></li>
						</security:authorize>

					</ul></li>
			</ul>
		</nav>

		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="insert-bqr" href="#"
					class="nav-link"> <em class="nav-icon fas fa-indent"></em>
						<p class="font-weight-bold">
							Business MIS<em class="right fas fa-angle-left"></em>
						</p>
				</a>
					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/businessmis/businessmis-update"
								class="nav-link"> <em
									class="far fa-arrow-alt-circle-right nav-icon"></em>
									<p>Update MIS</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN' , 'ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/businessmis/businessmis-download"
								class="nav-link"> <em class="fa fa-download nav-icon"></em>
									<p>Download MIS</p>
							</a></li>
						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/merchant-report/download-merchant-report"
								class="nav-link"> <em class="nav-icon fa fa-download"></em>
									<p>
										Transaction MIS Report</em>
									</p>
							</a></li>
						</security:authorize>
					</ul></li>
			</ul>
		</nav>
		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a href="#" class="nav-link">
						<em class=" fa fa-file nav-icon"></em>
						<p class="font-weight-bold">
							Transaction Posting<em class="right fas fa-angle-left"></em>
						</p>
				</a>
					<ul class="nav nav-treeview">
						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/txn-posting/upload"
								class="nav-link"> <em class="fa fa-upload nav-icon"></em>
									<p>Upload</p>
							</a></li>
						</security:authorize> --%>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN', 'ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/txn-posting/list-view"
								class="nav-link"> <em class="fa fa-search nav-icon "></em>
									<p>List</p>
							</a></li>
						</security:authorize>
					</ul></li>
			</ul>
		</nav>
		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="settlement-upload"
					href="#" class="nav-link"> <em class="nav-icon fa fa-qrcode"></em>
						<p class="font-weight-bold">
							Settlement<em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">
						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item has-treeview "><a
								href="${pageContext.servletContext.contextPath}/settlement/upload"
								class="nav-link"> <em class=" fa fa-upload nav-icon"></em>
									<p class="font-weight-bold">Manual Settlement</p>
							</a></li>
						</security:authorize> --%>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item has-treeview "><a
								href="${pageContext.servletContext.contextPath}/amex-settlement/view"
								class="nav-link"> <em class=" fa fa-list nav-icon"></em>
									<p class="font-weight-bold">Amex Settlement</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/periodicSettlement/autosettlement-time-view"
								class="nav-link"> <em class="fa fa-plus-square nav-icon"></em>
									<p>Customized Settlement</p>
							</a></li>
						</security:authorize>
					</ul></li>
			</ul>
		</nav>


		<%-- 		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="static-qr" href="#"
					class="nav-link"> <em class="nav-icon fa fa-qrcode"></em>
						<p class="font-weight-bold">
							Static Qr<em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">
						 <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/static-qr/upload"
								class="nav-link"> <em class="nav-icon fa fa-upload"></em>
									<p>
										Upload Static QR</em>
									</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/static-qr/download-list"
								class="nav-link"> <em class="nav-icon fa fa-download"></em>
									<p>
										Download Static QR</em>
									</p>
							</a></li>
						</security:authorize>
					</ul></li>
			</ul>
		</nav>
 --%>
		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a href="#" class="nav-link">
						<em class=" fa fa-location-arrow nav-icon"></em>
						<p class="font-weight-bold">
							Add Location<em class="right fas fa-angle-left"></em>
						</p>
				</a>
					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/location/add-country"
								class="nav-link"> <em class="fa fa-city nav-icon"></em>
									<p>Add Country</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/location/add-state"
								class="nav-link"> <em class="fa fa-city nav-icon "></em>
									<p>Add State</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/location/add-city"
								class="nav-link"> <em class="fa fa-city nav-icon "></em>
									<p>Add City</p>
							</a></li>
						</security:authorize>

						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/location-list/country"
								class="nav-link"> <em class="fa fa-city nav-icon "></em>
									<p>Location List</p>
							</a></li>
						</security:authorize>


					</ul></li>
			</ul>
		</nav>

		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="dcc" href="#"
					class="nav-link"> <em class="nav-icon fa fa-qrcode"></em>
						<p class="font-weight-bold">
							DCC <em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">
						<%-- 	<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/dcc-upload/upload-dcc-data"
								class="nav-link"> <em class=" fa fa-upload nav-icon"></em>
									<p class="font-weight-bold">DCC Bin Based Upload</p>
							</a></li>
						</security:authorize> --%>
						<%-- <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">

							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/dcc-upload/dcc-currency-upload-view"
								class="nav-link"> <em class=" fa fa-upload nav-icon"></em>
									<p class="font-weight-bold">DCC Currency Code Upload</p>
							</a></li>
						</security:authorize> --%>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/dcc-upload/download-dcc-bin"
								class="nav-link"> <em class="nav-icon fa fa-download"></em>
									<p>
										DCC Bin Download</em>
									</p>
							</a></li>
						</security:authorize>
					</ul></li>
			</ul>
		</nav>

		<!--  TG Users tab-->
		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="user-tg" href="#"
					class="nav-link"> <em class="fas fa-user-shield nav-icon"></em>
						<p class="font-weight-bold">
							TG<em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">


						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/users-tg/create-tg-user"
								class="nav-link"> <em class="nav-icon fa fa-user"></em>
									<p>
										Create Tg User</em>
									</p>
							</a></li>
						</security:authorize>
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/users-tg/users-tg-list"
								class="nav-link"> <em class="nav-icon fa fa-list"></em>
									<p>
										List of Tg User</em>
									</p>
							</a></li>
						</security:authorize>


					</ul></li>
			</ul>
		</nav>

		<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="user-tg" href="#"
					class="nav-link"> <em class="fas fa-user-shield nav-icon"></em>
						<p class="font-weight-bold">
							Reports<em class="right fas fa-angle-left"></em>
						</p>
				</a>

					<ul class="nav nav-treeview">



						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_USER','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/snapbiz/upload-view"
								class="nav-link"> <em class="nav-icon fa fa-file"></em>
									<p>
										Snapbiz</em>
									</p>
							</a></li>
						</security:authorize>


					</ul></li>
			</ul>
		</nav>
		<%-- 	<nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="user-tg" href="#"
					class="nav-link"> <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<em class="fas fa-user-shield nav-icon"></em>

							<p class="font-weight-bold">
								Bin Details<em class="right fas fa-angle-left"></em>
							</p>
						</security:authorize>
				</a>

					<ul class="nav nav-treeview">
						<security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')">
							<li class="nav-item"><a
								href="${pageContext.servletContext.contextPath}/binDetails/upload-download-view"
								class="nav-link"> <em class="nav-icon fa fa-upload"></em>
									<p>Bin Upload/Download</p>
							</a></li>
						</security:authorize> 
					</ul></li>
			</ul>
		</nav> --%>

		<!-- <nav class="mt-2" aria-label="Site-Menu">
			<ul class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview" role="menu" data-accordion="false">
				<li class="nav-item has-treeview "><a id="user-tg" href="#"
					class="nav-link"> <security:authorize
							access="hasAnyRole('ROLE_SITE_ADMIN','ROLE_SITE_SUPER_ADMIN')"><em class="fas fa-user-shield nav-icon"></em>
					
						<p class="font-weight-bold">
							Upload Download<em class="right fas fa-angle-left"></em>
						</p>
						</security:authorize>
				</a>
							
					
					</li>
			</ul>
		</nav> -->
		<!-- /.sidebar-menu -->
	</div>
	<!-- /.sidebar -->
</aside>
