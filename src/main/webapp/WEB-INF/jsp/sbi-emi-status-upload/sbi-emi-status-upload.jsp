<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">


<head>
<meta charset="ISO-8859-1">
<title>Sbi Emi Status Upload</title>
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
<%@ include file="../fragments/header-css.jsp"%>
</head>

<body class="hold-transition sidebar-mini layout-fixed">

	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>

		<div class="content-wrapper">
			<div class="container">
				<c:if test="${ invalid != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						Invalid excel file. Please upload a valid excel file.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<c:if test="${ emi_upload_format_error != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						Unable to download the sbi emi status upload format.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<div class="card" id="container-card">
					<div class="card-header">
						<div class="row">
							<div class="col">
								<h6 class="mb-0">Sbi Emi Status Upload</h6>
							</div>
							<div class="col-auto">
							<ol class="breadcrumb float-sm-right bg-white mb-0 pl-0"
									id="breadcrumb">
									<li class="breadcrumb-item"><a href="${pageContext.servletContext.contextPath}/tg/upload-common"><i
									class="fa fa-backward" aria-hidden="true"></i> Common Upload</a></li>
								
								</ol>
							
								
							</div>
						</div>
						
						<div class="row d-flex justify-content-end">
							
							<div class="col-auto">
							
								<a
				                href="${pageContext.servletContext.contextPath}/sbi-emi-status-upload/sbiemi-upload-format"
									id="download-bulk-upload-format" role="button"
									class="btn btn-primary text-white btn-md float-right"> <em
									class="fas fa-download"></em> Download Sbi Emi Status Upload Format
								</a>
								
							</div>
						</div>
					</div>
					<div class=" mx-auto col-sm-7">
					<div class="card-body mb-5">
						<form id="emi-upload" enctype="multipart/form-data"
							action="${pageContext.servletContext.contextPath}/sbi-emi-status-upload/sbiemistatus-upload"
							method="post" >
							<input type="hidden" name="${_csrf.parameterName}"
								value="${_csrf.token}">
							<div class="row">
								<div class="col-sm-12 col-xs-6" id="submitInput">
									<div class="custom-file">
										<input type="file" class="custom-file-input"
											id="file"
											aria-describedby="inputGroupFileAddon01" name="file">
										<label class="custom-file-label" for="inputGroupFile01">Choose
											file</label>
											<div id="file_alert" class="validationAlert"></div>										
										<input type="hidden" name="${_csrf.parameterName}"
											value="${_csrf.token}">
									</div>
								</div>
								<div class="col-sm-2 col-xs-6 mt-3 mx-auto d-block" id="submitBlock">
									<button class="btn btn-primary mt-5 mt-sm-0  mx-auto d-block"
										type="submit" id="submit">Submit</button>
								</div>
							</div>
						</form>
						<div class="btn btn-primary col-sm-2 mx-auto" id="reloadPage">Upload File</div>
					</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<%@ include file="../fragments/footer-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/sbi-emi-status-upload/sbi-emi-status-upload.js"></script>
</body>

</html>