<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="ISO-8859-1">
<title>Create Merchant Referall</title>
<%@ include file="../fragments/header-css.jsp"%>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>

		<div class="content-wrapper">

			<div class="container">

				<c:if test="${ msg == true }">

					<div id="card-alerts"
						class="alert alert-success alert-dismissible fade show"
						role="alert">
						Referral Successfully Created.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>

				<c:if test="${ referralExists == true }">

					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						Referral already exists.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>

				<div class="card" id="container-card">

					<div class="card-header">
						<div class="row">
							<h6 class="mb-0">Create Referral</h6>
						</div>
					</div>

					<div class="card-body">
						<form:form
							action="${pageContext.servletContext.contextPath}/merchant-referall/add-merchant-referall"
							id="addNewReferral" method="post"
							modelAttribute="referralCrudBean">
							<input type="hidden" name="${_csrf.parameterName}"
								value="${_csrf.token}">
							<div class="row">

								<div class="col">
									<div class="form-group input-group-sm">
										<label for="refName">Referral Name*</label>
										<form:input type="text" class="form-control no-special-char"
											name="refName" id="refName" placeholder="Referral Name"
											path="refName" maxlength="100" autocomplete="off" />
										<form:errors path="refName" class="validationAlert" />
										<div id="refName_alert" class="validationAlert"></div>
									</div>
								</div>

								<div class="col">

									<div class="form-group input-group-sm">
										<label for="refName">Is Active*</label>
										<form:input type="text" class="form-control no-special-char"
											name="isActive" id="isActive" placeholder="Is Active"
											path="isActive" maxlength="100" autocomplete="off" />
										<form:errors path="isActive" class="validationAlert" />
										<div id="isActive_alert" class="validationAlert"></div>
									</div>
								</div>
							</div>
							<div class="row mt-3">
								<div class="mx-auto d-block">
									<button type="button" class="btn btn-primary" value="Submit"
										id="addReferral">
										<i class="far fa-arrow-alt-circle-right" aria-hidden="true"></i>
										Create Referral
									</button>
								</div>
							</div>
						</form:form>
					</div>
				</div>


			</div>

		</div>
	</div>




	<%@ include file="../fragments/footer-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/merchant-referall/merchant-referall-listing.js"></script>
</body>

</html>
