<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="ISO-8859-1">
<title>Download IMS</title>
<%@ include file="../fragments/header-css.jsp"%>
<link
	href="${pageContext.servletContext.contextPath}/resources/css/bootstrap-datepicker-min.css"
	rel="stylesheet" />
<link rel="stylesheet"
	href="${pageContext.servletContext.contextPath}/resources/css/select.min.css">
</head>
<body class="hold-transition sidebar-mini layout-fixed">
	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>
		<div class="content-wrapper">
			<div class="container">
				<c:if test="${ msg != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						No Data Available
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<c:if test="${ invalid != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						TID not valid
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<div class="card" id="container-card">
					<div class="card-header">
						<div class="row">
							<div class="col">
								<h6 class="mb-0">IMS Download</h6>
							</div>
						</div>
					</div>
					<form:form
						action="${pageContext.servletContext.contextPath}/IMS/download-IMS"
						id="downloadIMS" method="post" modelAttribute="IMSBean">

						<input type="hidden" name="${_csrf.parameterName}"
							value="${_csrf.token}">
						<div class="card-body">
							<div class="row">

								<div class="col-sm-3">
									<div class="">
										<div class="form-group  ">
											<label class="mr-2">From Date </label>
											<div class="input-group input-group-sm">
												<div class="input-group-prepend">
													<span class="input-group-text"> <em
														class="far fa-calendar-alt"></em>
													</span>
												</div>
												<input type="text" class="form-control float-right"
													id="fromDate" name="fromDate" placeholder="DD/MM/YYYY"
													autocomplete="off" value="">
											</div>
											<!-- /input group -->
											<div id="fromDate_error" class="validationAlert"></div>
											<div id="30days_error" class="validationAlert"></div>
										</div>
										<!--/to date -->
									</div>
								</div>
								<div class="col-sm-3">
									<!--   /from date -->
									<div class="form-group">
										<label class="mr-2"> To Date</label>&nbsp;
										<div class="input-group input-group-sm">
											<div class="input-group-prepend">
												<span class="input-group-text"> <em
													class="far fa-calendar-alt"></em>
												</span>
											</div>
											<input type="text" class="form-control float-right"
												name="toDate" id="toDate" placeholder="DD/MM/YYYY"
												autocomplete="off" value="">
										</div>
										<!-- /input group -->
										<div id="toDate_error" class="validationAlert"></div>
										<div id="exceeddays_error" class="validationAlert"></div>
									</div>
								</div>
								<div class="col-sm-3">
									<div class="form-group input-group-sm">
										<label>Mobile Number</label> <input
											class="form-control no-special-char" name="mobileNo" value=""
											type="text" placeholder="Mobile No" id="mobileNo"
											autocomplete="off" maxlength="10">
										<div id="mobileNo_error" class="validationAlert"></div>
									</div>
								</div>

								<div class="col-sm-3">
									<div class="form-group input-group-sm">
										<label>Status</label> <select
											class="form-control  no-special-char"
											id="status" name="status">
											<option value="0">Pending</option>
											<option value="2">Allocated</option>
											<option value="3">Allocation Rejected</option>
											<option value="4">Assigned</option>
											<option value="5">Rejected</option>
											<option value="10">All</option>
										</select>
									</div>
								</div>
							</div>

						</div>
						<!-- search button -->
						<div class="card-footer pb-0  mb-4 pt-0 bg-white">
							<div class="row">
								<div class="d-flex mx-auto">
									<button type="submit" id="download"
										class="btn btn-md btn-primary mr-2 dvfont">
										<i class="fa fa-download" aria-hidden="true"></i> Submit
									</button>
								</div>
							</div>
							<!-- /search button -->
						</div>
				</div>
				<!-- table list view -->
				<div class="card-body pt-0 pb-2" id="txnposting_table">
					<div class="table-responsive">
						<input type="hidden" name="${_csrf.parameterName}"
							value="${_csrf.token}">

					</div>
				</div>

				</form:form>
			</div>
		</div>
	</div>
	</div>
	<%@ include file="../fragments/footer-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/ims/ims-download.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/bootstrap-datepicker-min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/select.min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/validator.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/moment.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/locale/de.js"></script>
</body>
</html>
