<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="ISO-8859-1">
<title>Add State</title>
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
<%@ include file="../fragments/header-css.jsp"%>
<link rel="stylesheet"
	href="${pageContext.servletContext.contextPath}/resources/css/select.min.css">
</head>
<body class="hold-transition sidebar-mini layout-fixed">
	<div class="wrapper">
		<%@include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>
		<div class="content-wrapper">
			<div class="container">
				<c:if test="${msg ne null}">
					<c:choose>
						<c:when test="${msg eq true}">
							<div id="card-alerts"
								class="alert alert-success alert-dismissible fade show"
								role="alert">
								State Details Added Successfully
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
						</c:when>
						<c:otherwise>
							<div id="card-alerts"
								class="alert alert-danger alert-dismissible fade show"
								role="alert">
								${msg}
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
						</c:otherwise>
					</c:choose>
				</c:if>
				<c:if test="${ stateExists == true }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						State already exists.
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
				<div class="card" id="container-card">
					<div class="card-header">
						<h6 class="mb-0">Add State</h6>
					</div>
					<div class="card-body">
						<form:form id="addStateBean" name="addStateBean"
							action="${pageContext.servletContext.contextPath}/location/add-state"
							method="post" modelAttribute="addStateBean">
							<form:input type="hidden" path="stateId" id="stateId"
								name="stateId" />
							<input type="hidden" name="${_csrf.parameterName}"
								value="${_csrf.token}">
							<div class="row">
								<div class="col">
									<div class="form-group input-group-sm">
										<label for="stateName">State Name<span
											class="text-danger">*</span></label>
										<form:input type="text" class="form-control no-special-char"
											name="stateName" id="stateName" placeholder="State Name"
											path="stateName" maxlength="100" />
										<form:errors path="stateName" class="validationAlert" />
										<div id="stateName_error" class="validationAlert"></div>
									</div>
								</div>
								<div class="col">
									<div class="form-group input-group-sm">
										<label for="stateCode">State Code<span
											class="text-danger">*</span></label>
										<form:input type="text" class="form-control no-special-char"
											name="stateCode" id="stateCode" placeholder="State Code"
											path="stateCode" maxlength="10" />
										<form:errors path="stateCode" class="validationAlert" />
										<div id="stateCode_error" class="validationAlert"></div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col">
									<div class="form-group input-group-sm">
										<label for="countryId">Country Name<span
											class="text-danger">*</span></label>
										<form:select path="countryId" class="form-control">
											<form:option value="0">Select Country</form:option>
											<c:forEach items="${countries}" var="count">
												<form:option value="${count.getKey() }"> ${count.getValue() } </form:option>
											</c:forEach>
										</form:select>
										<form:errors path="countryId" class="validationAlert" />
										<div id="countryId_error" class="validationAlert"></div>
									</div>
								</div>
								<div class="col"></div>
							</div>
							<!-- submit button -->
							<div class="card-footer pb-0  mt-2 bg-white">
								<div class="row">
									<div class="d-flex mx-auto">
										<button id="submitBtn" type="submit"
											class="btn btn-md btn-primary mr-2 dvfont" title="Submit">
											<em class="fa fa-plus-square"> </em> Add State
										</button>
									</div>
								</div>
							</div>
							<!-- /submit button -->
						</form:form>
					</div>
				</div>
				<!-- end of table list view -->
			</div>
		</div>
	</div>
	<%@ include file="../fragments/footer-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/location/add-state.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/select.min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/validator.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/moment.js"></script>
</body>
</html>