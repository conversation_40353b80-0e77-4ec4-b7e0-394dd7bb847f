<%@ taglib prefix="security" uri="http://www.springframework.org/security/tags" %>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib uri="jakarta.tags.core" prefix="c"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>

<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="ISO-8859-1">
	<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
	<meta id="app_context_path" name="app_context_path" content="${pageContext.servletContext.contextPath}"/>
	<title>
		Add Pincode
	</title>
	<%@ include file="../fragments/header-css.jsp"%>
	<link rel="stylesheet" href="${pageContext.servletContext.contextPath}/resources/css/select.min.css">
</head>

<body class="hold-transition sidebar-mini layout-fixed">

	<div class="wrapper">
		<%@ include file="../fragments/header.jsp" %>
		<%@ include file="../fragments/aside.jsp" %>

		<div class="content-wrapper">

			<div class="container">
				<c:if test="${msg ne null}">
					<c:choose>
						<c:when test="${msg eq true}">
							<div id="card-alerts"
								class="alert alert-success alert-dismissible fade show"
								role="alert">
								Pincode Added Successfully
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
						</c:when>
						<c:otherwise>
							<div id="card-alerts"
								class="alert alert-success alert-dismissible fade show"
								role="alert">
								${msg}
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
						</c:otherwise>
					</c:choose>
				</c:if>
				
				<div id="card-alerts1" class="alert alert-danger alert-dismissible fade show"  role="alert" style="display:none">
							
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
			 </div>
			 
			 <div id="card-alerts2" class="alert alert-danger alert-dismissible fade show"  role="alert" style="display:none">
							Pincode does not exists
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
			 </div>
				
				<div class="card" id="container-card">
					
					<div class="card-header">								
						<h6 class="mb-0">Add Pincode</h6>
					</div>
					<div class="card-body">
					<form:form id="pincodeBean" name="pincodeBean"
							action="${pageContext.servletContext.contextPath}/location/add-pincode"
							method="post" modelAttribute="pincodeBean">
							
							<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}">
							 <div class="row  mb-sm-1">
								<div class="col-sm-4">
									<div class="form-group input-group-sm">
										<label for="merchantId">Pincode<span
											class="text-danger">*</span></label>
										<form:input type="text" id="pincode" class="form-control" name="pincode" maxlength="6" 
										placeholder="Pincode" autocomplete="off" path="pincode"/>
											<form:errors path="pincode" class="validationAlert" />
										<div id="pincode_error" class="validationAlert"></div>
									</div>
								</div>

								<div class="col-sm-4">
									<div class="form-group input-group-sm">
										<label >State<span
											class="text-danger">*</span></label>
									<form:input type="text" id="state"
								class="form-control" name="state"   placeholder="State" autocomplete="off" 
								maxlength="100" path="state" readonly="true"/>
								<form:errors path="state" class="validationAlert" />
								<div id="state_error" class="validationAlert"></div>
										
									</div>
								</div>
								
								<div class="col-sm-4">
									<div class="form-group input-group-sm">
										<label >City<span
											class="text-danger">*</span></label>
								<form:input type="text" id="city" class="form-control" name="city" placeholder="City" autocomplete="off" 
								maxlength="100" path="city" readonly="true"/>
									<form:errors path="city" class="validationAlert" />
										<div id="city_error" class="validationAlert"></div>		
									</div>
								</div>
							</div> 
							
				<div class="row mt-3 justify-content-md-center">
					<div class="mx-auto d-block">
					
						   <button type="button" class="btn btn-danger dvfont" id= "check" value="Check">
							<i class="far fa-arrow-alt-circle-right" aria-hidden="true"></i>
							 Check
							
			              </button> 
						&nbsp;
						<button type="submit" class="btn btn-primary dvfont" id="submit" value="Submit">
							<i class="far fa-arrow-alt-circle-right" aria-hidden="true"></i>
							 Add Pincode
							
						</button>
						
						<!-- --------------------My Task---------------------------------- -->
						
						<button type="button" class="btn btn-primary dvfont" id="download" value="Check">
							<i class="far fa-arrow-alt-circle-right" aria-hidden="true"></i>
							Download
							
						</button>
						
					<%-- 		<!-- start of post method -->
							<div class="card" id="container-card">

								<div class="">
									<form id="downloadForm2"
										action="${pageContext.servletContext.contextPath}/location/pincode-download"
										method="post">
										<input type="hidden" name="${_csrf.parameterName}"	value="${_csrf.token}">
							
								
									</form>
									<div class="row"></div>
								</div>

							</div>
							<!-- End of post method --> --%>
						
						<!-- --------------------My Task---------------------------------- -->
					 
				
					</div>
				</div>
			</form:form>
			
			</div>
	
		</div>
</div>
	</div></div>

	
	

	<%@ include file="../fragments/footer-js.jsp" %>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/location/add-pincode.js"></script> 
		<script src="${pageContext.servletContext.contextPath}/resources/js/select.min.js"></script>
		<script src="${pageContext.servletContext.contextPath}/resources/js/validator.js"></script> 
	
</body>

</html>