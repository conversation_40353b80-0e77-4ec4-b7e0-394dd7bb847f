<%@ taglib prefix="security"
	uri="http://www.springframework.org/security/tags"%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="ISO-8859-1">
<meta id="_csrf" name="_csrf" content="${_csrf.token}" />
<meta id="_csrf_header" name="_csrf_header"
	content="${_csrf.headerName}" />
<meta id="app_context_path" name="app_context_path"
	content="${pageContext.servletContext.contextPath}" />
<title>Bin Category List and Download</title>
<%@ include file="../fragments/header-css.jsp"%>
<%@ include file="../fragments/datatables-css.jsp"%>
<link
	href="${pageContext.servletContext.contextPath}/resources/css/bootstrap-datepicker-min.css"
	rel="stylesheet" />
<link rel="stylesheet"
	href="${pageContext.servletContext.contextPath}/resources/css/select.min.css">
	
	
	<style>
#f::before ,#f::after {
  content: '';
}

 #card-alerts3 {
	display: none;
} 

#card-alerts4 {
	display: none;
}

}
</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed">
	<div class="wrapper">
		<%@ include file="../fragments/header.jsp"%>
		<%@ include file="../fragments/aside.jsp"%>
		<div class="content-wrapper">
			<div class="container-fluid">

					<c:if test="${ invalid != null }">
					<div id="card-alerts"
						class="alert alert-danger alert-dismissible fade show"
						role="alert">
						No Data Available
						<button type="button" class="close" data-dismiss="alert"
							aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</c:if>
			
	<div id="card-alerts3" class="alert alert-success alert-dismissible fade show"
								role="alert">
								
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
							
		<div id="card-alerts4" class="alert alert-danger alert-dismissible fade show"
								role="alert">
								
								<button type="button" class="close" data-dismiss="alert"
									aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
							</div>					

				<div class="card" id="container-card">
					<div class="card-header">
						<div class="row">

							<div class="col">
								<h6 class="mb-0">List and Download Bin Category</h6>
							</div>
				
						</div>
					</div>

					<div class="card-body">
						<form
							action="${pageContext.servletContext.contextPath}/binDetails/bin-category-download"
							id="downloadBinCategory" method="post">
							<input type="hidden" name="${_csrf.parameterName}"
								value="${_csrf.token}">
							<div class="row ">

								<div class="col-12 col-sm-6 col-md-4 my-2">
									<label>Start Date </label>
									<div class="input-group input-group-sm">
										<div class="input-group-prepend">
											<span class="input-group-text"> <em
												class="far fa-calendar-alt"></em></span>
										</div>
										<input type="text" class="form-control" id="fromdate"
											name="startDate" placeholder="DD/MM/YYYY" autocomplete="off" />
									</div>
									<div id="fromdate_error" class="validationAlert"></div>
									<div id="30days_error" class="validationAlert"></div>
								</div>
								<div class="col-12 col-sm-6 col-md-4 my-2">
									<label>End Date </label>
									<div class="input-group input-group-sm">
										<div class="input-group-prepend">
											<span class="input-group-text"> <em
												class="far fa-calendar-alt"></em></span>
										</div>
										<input type="text" class="form-control" id="todate"
											name="endDate" placeholder="DD/MM/YYYY" autocomplete="off" />
									</div>
									<div id="todate_error" class="validationAlert"></div>
									<div id="exceeddays_error" class="validationAlert"></div>
								</div>

								<div class="col-12 col-sm-6 col-md-4 my-2">
									<label>Code</label>
									<div class="input-group input-group-sm">

										<input type="text" id="code" class="form-control"
											placeholder="Code" name="code" value=""
											autocomplete="off" maxlength="30" />
								
											
									</div>
									<div id="code_error" class="validationAlert"></div>
								</div>

						

								<div class="col-12 col-sm-6 col-md-4 my-2">
									<label> Status</label>
									<div class="input-group input-group-sm">

										<select id="status" name="status"
											class="form-control">
											<option value="-1" selected="selected">Both</option>
											<option value="1" >Active</option>
											<option value="0">Not Active</option>
										
										</select>
									</div>
								</div>
							</div>


							<div class="row mt-3 justify-content-center">
								<div class="">
								
									<button type="button"
										class="btn btn-primary dvfont mx-auto d-block" value="button"
										id="searchBin">
										<i class="fa fa-search" aria-hidden="true"></i> Search
									</button>
									</div>
									<div class = "ml-3">
									<button type="button"
										class="btn btn-primary dvfont mx-auto d-block" value="button"
										id="downloadBin">
										<i class="fa fa-download" aria-hidden="true"></i> Download
									</button>
									</div>
								
							</div>
							
									<div class="card-body" id="first-search">
							<div class="table-responsive">


														<!-- Data Tables header -->
								<table id="table_id"
									class="display table table-bordered table-sm mx-auto w-100"
									aria-describedby="">
									<thead>
										<tr>
										<th id="f">Sr No.</th>
										<th id="f">Name</th>
										<th id="f">Code</th>
										<th id="f">Description</th>
										<th id="f">Status</th>
										<th id="t">Edit Status</th>
										<th id="">Start Date</th>
										<th id="">End Date</th>
										<th id="">Bin Type</th>
										<th id="">Bin Length</th>
										<th id="">Record Created</th>
									
										<th id="">Record Updated</th>
							
										
										</tr>
									</thead>
								</table>
								<!-- Data Tables header End-->

							</div>
						</div>
						
							<div id="loader">
								<div class="loader loader2"></div>
								<div>
									<img
										src="${pageContext.servletContext.contextPath}/resources/images/logo.png"
										alt="mosambee logo"
										class=" animation: zoom-in-zoom-out loader1 img-circle mb-1 mr-2" />
								</div>
							</div>

						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
	<%@ include file="../fragments/footer-js.jsp"%>
		<%@ include file="../fragments/datatables-js.jsp"%>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/bin-details-csv-download/bin-category-list.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/bootstrap-datepicker-min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/select.min.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/resources/js/validator.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/moment.js"></script>
	<script
		src="${pageContext.servletContext.contextPath}/webjars/momentjs/2.24.0/locale/de.js"></script>
</body>
</html>
