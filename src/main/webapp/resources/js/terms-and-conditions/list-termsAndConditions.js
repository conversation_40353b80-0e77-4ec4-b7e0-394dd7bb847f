$(document).ready(function () {
	
	$("#termslist2").attr('class','nav-link').css('color','#343a40');
	$("#termslist1").css('background-color','rgba(255,255,255,.9)');
	
	// For Merchant dropdown
	$('.select2').select2();
	
	//$("#dataTableBody").hide();
	
	$('#card-alerts').show();
	$("#card-alerts").alert();
    
	//time for alert error message to be shown
	setTimeout(function () {
		$(".alert").alert('close');
	}, 4000);
	
	
    // DataTable
    var token = $('#_csrf').attr('content');
    var header = $('#_csrf_header').attr('content');
    var postdata = {};
    
    // Data Table ajax call and storing the value in table.
    var table = $('#terms-table-id').DataTable({
        "processing": true,
        "serverSide": true,
        "scrollCollapse": true,
        "paging": true,
        "createdRow": function (row, data, index) {
            var info = table.page.info();
            $('td', row).eq(0).html(index + 1 + info.page * info.length);
        },
        "dom": 
        	"<'row'<'col-sm-12 col-md-12'l>>" +
			"<'row'<'col-sm-12'tr>>" +
			"<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        
        "ajax": {
            "url": contexturi + "/terms-and-conditons/list-terms",
            "contentType": "application/json",
            "type": "POST",
            "data": function (d) {
            	postdata.dtRequest = d;
                postdata.merchantName = $('#merchantName').val();
                return JSON.stringify(postdata);
            },
            "beforeSend": function (request) {
                request.setRequestHeader(header, token);
            },
            "error": function (xhr, error, code) {

                if (error === 'parsererror' || error === 'timeout' || error === 'error') {
                    window.location.href = myContextPath + "/login?invalid";
                }
            }
        },
        "columns": [
        	{ "data": 'srNo'},
        	{ "data": 'merchantName'},//businessName in db
            { "data": 'header' },
            { "data": 'status' },
            { "defaultContent": '<button type="button" title="Edit" class="btn btn-primary btn-xs mx-auto d-block" data-toggle="modal" data-target="#modal-default" id="update"><i class="fa fa-edit"></i></button>' }
        ],
        "order": [[0, "asc"]]
    });
    
   
    // to EDIT or UPDATE Terms
    $("#terms-table-id tbody").on("click", "#update", function () {
    	var data = table.row($(this).parents("tr")).data();
        var mtcId = data['mtcId'];
        $form = $("<form action='" + contexturi + "/terms-and-conditons/list-terms-edit' method='post'></form>");
        $form.append("<input type='hidden' name='_csrf' value='" + $("#_csrf").attr('content') + "'>");
        $form.append("<input type='hidden' name = 'mtcId' value='" + mtcId + "'>");
        $('body').append($form);
       	$form.submit();
    
    });
    
 // search function
    $("#search").on("click", function () {
    	
        	//$("#dataTableBody").show();
            $('#terms-table-id').dataTable().fnFilter();
    	
    });
    
    
    
});
