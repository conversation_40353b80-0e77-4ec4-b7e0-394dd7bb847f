$(document).ready(function () {
  // Datepicker Implementation
  var date = new Date(),
    yr = date.getFullYear(),
    month = date.getMonth() + 1,
    day = date.getDate(),
    todayDate = day + "-" + month + "-" + yr;

  var maxDate;
  var minDate;

  //activation request date
  $("#fromdate")
    .datepicker({
      format: "dd-mm-yyyy",
      autoclose: true,
    })
    .click("changeDate", function (selected) {
      minDate = moment(todayDate, "DD-MM-YYYY")
        .subtract(20, "years")
        .format("DD-MM-YYYY");
      maxDate = todayDate;
      $("#fromdate").datepicker("setStartDate", minDate);
      $("#fromdate").datepicker("setEndDate", maxDate);
    });

  //to date
  $("#todate")
    .datepicker({
      format: "dd-mm-yyyy",
      autoclose: true,
    })
    .click("changeDate", function (selected) {
      var maxDate = todayDate;
      var minDate = $("#fromdate").val();
      $("#todate").datepicker("setStartDate", minDate);
      $("#todate").datepicker("setEndDate", maxDate);
    });

  if (
    $.trim($("#fromdate").val()) == "" ||
    $.trim($("#fromdate").val()) == "undefined"
  ) {
    $("#fromdate").datepicker("setDate", todayDate);
  }

  if (
    $.trim($("#todate").val()) == "" ||
    $.trim($("#todate").val()) == "undefined"
  ) {
    $("#todate").datepicker("setDate", todayDate);
  }
  // End Datepicker Implementation

  $("#openModal").click(function (event) {
    if (!validateMqrReportForm()) {
      return false; // Stop form submission if validation fails
    }
    
   
    $("#downloadModal").modal("show");
  });
  
  $('#downloadModal').on('hidden.bs.modal', function () {

    $(this).find('input').val('');
    
  
});

  $("#confirmDownload").click(function (event) {
	 // var alphaNumRegex = /^[a-zA-Z0-9]+$/;

	    if (
      $.trim($("#fileName").val()) == ""
    ) {
      $("#fileName_error").text("Please enter File Name ");

      return false;
    }
    
/*      if ($.trim($("#fileName").val()) != "") {
			if (!alphaNumRegex.test($.trim($("#fileName").val()))) {
				$("#fileName_error").text(
					"Please enter a valid File Name"
				);
				return false;
			}
	}*/
	

    var fileName = $("#fileName").val();

    if (!fileName.endsWith(".xlsx")) {
      fileName += ".xlsx";
    }
    
  const fromDate = $("#fromdate").val();
        const toDate = $("#todate").val();

        // Function to convert date from dd-mm-yyyy to yyyy-mm-dd
        const formatDate = (date) => {
            const dateParts = date.split("-");
            return `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
        };

        if (fromDate && toDate) {
            // Convert both dates
            const formattedFromDate = formatDate(fromDate);
            const formattedToDate = formatDate(toDate);

      
            
            

    var jsonData = {
      fromDate: formattedFromDate,
      toDate: formattedToDate,
      posIdList: $("#posIdList").val().split(",").filter((el) =>{ return el}),
      entId: $("#entId").val(),
      mid: $("#mid").val(),
      tid: $("#tid").val(),
       acquirerName: $("#acquirerName").val(),
      responseType: "blob",
    };
    

 var baseUrl = $("#baseUrl").val()

    $.ajax({
      url:  baseUrl + "/summaryreport/mpr-report/download-report",

      headers: {
        "X-CSRF-TOKEN": $("#_csrf").attr("content"),
      },
      type: "POST",
     xhrFields: {
        responseType: "blob",
      },
      contentType: "application/json",
      data: JSON.stringify(jsonData),
      success: function (result,status, xhr) {
		  
		 

      var blob = result
      var downloadUrl = URL.createObjectURL(blob);
        var a = document.createElement("a");
        a.href = downloadUrl;
        a.download = fileName;                          
        document.body.appendChild(a);
        a.click();
        
        $('#downloadModal').find('input').val('').end().modal('hide');
$('#downloadMPRFormat')[0].reset();

  $("#fromdate").datepicker("setDate", todayDate);
  
     $("#todate").datepicker("setDate", todayDate);
     
      },
      
      error : function(xhr, error, code) {
		  
	$("#card-alerts2").css("display", "block");


				setTimeout(function() { $("#card-alerts2").hide(); }, 4000);
		
		 $('#downloadModal').find('input').val('').end().modal('hide');
	
	
			

		}
    });

 }
  });


  
  
   $('#posIdList').on('input', function() {
     
        $('#entId').prop('disabled', $(this).val().length > 0);
      });

      $('#entId').on('input', function() {
      
        $('#posIdList').prop('disabled', $(this).val().length > 0);
      });

  function validateMqrReportForm() {
    var response = true;

    $(".validationAlert").text("");

    var start = $("#fromdate").datepicker("getDate");
    var end = $("#todate").datepicker("getDate");
    
   var numAndSpaceRegex = /^[0-9,]+$/;

    days = (end - start) / (1000 * 60 * 60 * 24);

    if ($.trim($("#fromdate").val()) == "") {
      $("#fromdate_error").text("Please select from date");
      response = false;
    } else if (Math.round(days) >= 10) {
      $("#30days_error").text("Please select dates between 10 days");
      response = false;
    }

    if ($.trim($("#todate").val()) == "") {
      $("#todate_error").text("Please select to date");
      response = false;
    } else if (start > end) {
      $("#exceeddays_error").text("Please select to date more than from date ");
      response = false;
    }

  /*  if (
      $.trim($("#posIdList").val()) == "" &&
      $.trim($("#entId").val()) == ""
    ) {
      $("#name_error").text("Required any one field(Pos Id or Enterprise Id )");

      return false;
    }*/
    
    if ($.trim($("#posIdList").val()) != "") {
			if (!numAndSpaceRegex.test($.trim($("#posIdList").val()))) {
				$("#name_error").text(
					"Only numbers are allowed"
				);
				return false;
			}
	}
	
	 

    return response;
  }
});
/**
 *
 */
/**
 *
 */
