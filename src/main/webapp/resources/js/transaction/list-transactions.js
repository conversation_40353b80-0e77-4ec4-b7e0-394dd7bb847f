var txnId1 = "";

$(document)
	.ready(
		function() {

			var now2 = new Date(Date.now() - 7.776e+9).toISOString().substring(0, 10);
			var format = now2.split("-").reverse().join("-");
			var date1 = $("#transactionDateFrom").val()
			var date2 = $("#transactionDateTo").val()
			var newdate = date1.split("-").reverse().join("-");
			var newdate2 = date2.split("-").reverse().join("-");
			var d1 = new Date(newdate)
			var d2 = new Date(now2);
			var d3 = new Date(newdate2)

			if (d1 <= d2 && d3 <= d2) {
				$("#merchantDate").val("1");
			}

			else {
				$("#merchantDate").val("0");
			}

			setTimeout(function() {
				if ($.trim($("#noncard1").val()) != '') {
					var data = $("#noncard1").val().split(',');

					for (let i = 0; i < data.length; i++) {

						$("#checkBox" + data[i]).prop('checked', true);
					}
				}

				if ($.trim($("#card1").val()) != '') {
					var data = $("#card1").val().split(',');


					for (let i = 0; i < data.length; i++) {

						$("#checkBox" + data[i]).prop('checked', true);
					}
				}
			}, 500);

			$('#exampleModal3').on('hidden.bs.modal', function() {
				$(':input', this).val('');
				$(".validationAlert").text("");
			});

			$('.select2').select2();

			$("#success").hide();
			$("#failed").hide();

			$('#exampleModal').on('hidden.bs.modal', function() {
				location.reload();
			});

			$('#exampleModal2').on('hidden.bs.modal', function() {
				location.reload();
			});



			var sfalg = $("#sfalg").val();


			$("#insert-bqr").attr('class', 'nav-link active');
			$(document).on('click', '#searchBusinessName li',
				function() {

					$("#merchantName").val(this.outerText);
					$("#container2").hide();

				});
			$("#searchBusinessName").hide();

			$("#search")
				.click(
					function(e) {
						e.preventDefault();

						$(".validationAlert").text("");

						var checkedTxnTypes = $('input[name="txnType"]:checked').map(function() {
							return $(this).val();
						}).get();

						if ($('#acquirer').val() == '3' && checkedTxnTypes.includes('0')) {
							$("#search_alert").text("Searching all transactions is not supported for HDFC.");
							return false;
						}


						if ($('input[name="txnType"]:checked').val() == '0' && ($('#acquirer').val() == '' || $('#acquirer').val() == null)) {
							$("#search_alert").text("Please select an acquirer.");
							return false;
						}



						if ($('input[name="txnType"]:checked').val() === undefined) {
							$("#search_alert").text("Please select transaction type");
							return false;
						}

						var fromDate = $("#transactionDateFrom")
							.datepicker("getDate");
						var toDate = $("#transactionDateTo")
							.datepicker("getDate");
						var Difference_In_Time = toDate
							.getTime()
							- fromDate.getTime();
						var Difference_In_Days = Difference_In_Time
							/ (1000 * 3600 * 24);

						if (Difference_In_Days > 30) {
							$("#transactionDateFrom_alert")
								.text(
									"Please select days less or equal to 30");
							return false;
						}
						if (Difference_In_Days < 0) {
							$("#transactionDateFrom_alert")
								.text(
									"Invalid Date Range! From Date cannot be after To Date!");
							return false;
						}

						$("#transactionSearch").submit();

					});



			$('#transactionSearch').submit(function() {

				var now2 = new Date(Date.now() - 7.776e+9).toISOString().substring(0, 10);
				var format = now2.split("-").reverse().join("-");
				var date1 = $("#transactionDateFrom").val()
				var date2 = $("#transactionDateTo").val()
				var newdate = date1.split("-").reverse().join("-");
				var newdate2 = date2.split("-").reverse().join("-");
				var d1 = new Date(newdate)
				var d2 = new Date(now2);
				var d3 = new Date(newdate2)



				if (d1 <= d2 && d3 <= d2) {
					$("#merchantDate").val("1");

				}

				else {
					$("#merchantDate").val("0");


				}




				if (d1 < d2 && d3 >= d2) {
					$("#transactionDateTo_alert").text('From date and To date should be either before or after ' + format + '');

					return false;

				}
				else {
					return true;
				}
			});

			$(document)
				.on(
					'keyup',
					'#merchantName',
					function() {
						$("#searchBusinessName").html("");
						$
							.ajax({
								url: contexturi
									+ "/transaction/get-merchant",
								headers: {
									'X-CSRF-TOKEN': $(
										'#_csrf').attr(
											'content'),
								},
								type: "POST",
								data: {
									name: $(this).val()
								},
								beforeSend: function() {

									$("#loader").show();
								},
								complete: function() {
									$("#loader").hide();


								},
								success: function(result) {
									var searchBusinessNameHtml = "";
									if (result.length) {
										result
											.forEach(function(
												item) {
												searchBusinessNameHtml += "<li class='liststyletype pl-2'>"
													+ item
													+ "</li>";
											});
										$(
											"#searchBusinessName")
											.html(
												searchBusinessNameHtml)
											.show();
									} else {
										$(
											"#searchBusinessName")
											.hide();
									}
									$("#container2").show();

								},
								error: function(xhr,
									error, code) {

									if (error === 'parsererror'
										|| error === 'timeout') {
										window.location.href = +contexturi
											+ "/login?invalid";
									}

								}
							});

					});




			$(document)
				.on(
					'click',
					'.showTransactionList1',
					function() {

						var txnId = $(this).data(
							'transactionid');

						$(".card-body #tranId").val(txnId);

						$
							.ajax({
								url: contexturi
									+ "/login-check/check-session",
								headers: {
									'X-CSRF-TOKEN': $(
										'#_csrf')
										.attr(
											'content'),
								},
								type: "POST",
								data: {

								},
								success: function(
									result) {

									if (/\bDOCTYPE\b/i.test(result)) {
										window.location.href = +contexturi
											+ "/login?invalid";
										return false

									}
									$("#exampleModal3")
										.modal('show');

								}
							});





					});

			$(document)
				.on(
					'click',
					'.showResponsedata',
					function() {

						var txnId = $(this).data(
							'transactionid');

						/*
						 * if ($("#declinedSettlement").is(
						 * ":checked") || $("#settlement").is(
						 * ":checked")) {
						 * 
						 * $form = $("<form action='" +
						 * contexturi +
						 * "/transaction/settlement'
						 * method='post'></form>"); $form
						 * .append("<input type='text'
						 * name='_csrf' value='" +
						 * $("#_csrf").attr( 'content') + "'>");
						 * $form .append("<input type='text'
						 * name = 'txnId' value='" + txnId +
						 * "'>"); $('body').append($form);
						 * $form.submit(); } else {
						 */
						$
							.ajax({
								url: contexturi
									+ "/transaction/get-transaction-data-id",
								headers: {
									'X-CSRF-TOKEN': $(
										'#_csrf').attr(
											'content'),
								},
								type: "POST",
								data: {
									resId: txnId
								},
								success: function(result) {

									$("#response-data")
										.html(result);
									$("#exampleModal")
										.modal('show');
								},
								error: function(xhr,
									error, code) {

									if (error === 'parsererror') {
										window.location.href = +contexturi
											+ "/login?invalid";
									}

								}
							});
						/*
						 * }
						 */
					});

			$(document)
				.on(
					'click',
					'.showTransactionList',
					function() {

						var txnId = $(this).data(
							'transactionid');
						var link = $("#receiptUrl").val()


						$
							.ajax({
								url: contexturi
									+ "/login-check/check-session",
								headers: {
									'X-CSRF-TOKEN': $(
										'#_csrf')
										.attr(
											'content'),
								},
								type: "POST",
								data: {

								},
								success: function(
									result) {

									if (/\bDOCTYPE\b/i.test(result)) {
										window.location.href = +contexturi
											+ "/login?invalid";
										return false

									}


									if ($("#declinedSettlement").is(
										":checked")
										|| $("#settlement").is(
											":checked")) {

										$form = $("<form action='"
											+ contexturi
											+ "/transaction/settlement' method='post'></form>");
										$form
											.append("<input type='text' name='_csrf' value='"
												+ $("#_csrf").attr(
													'content')
												+ "'>");
										$form
											.append("<input type='text' name = 'txnId' value='"
												+ txnId + "'>");
										$('body').append($form);
										$form.submit();
									} else {


										$
											.ajax({
												url: link + "/transactions/generate-receipt-url",
												headers: {
													'X-CSRF-TOKEN': $(
														'#_csrf')
														.attr(
															'content'),
												},
												type: "POST",
												contentType: "application/json",
												data: JSON.stringify({ txnId: txnId }),
												success: function(
													result) {


													window.open(result.shortUrl, '_blank');


												},
												error: function(xhr,
													error, code) {

													if (error === 'parsererror') {
														window.location.href = +contexturi
															+ "/login?invalid";
													}

												}
											});


									}

								}
							});

					});


			$(document)
				.on(
					'click',
					'.showTransactionList2',
					function() {

						var link2 = $("#merchantUrl").val()


						txnId1 = $(this).data(
							'transactionid');

						$
							.ajax({
								url: link2
									+ "/summaryreport/refund/getTransactionRefundHistory",
								headers: {
									'X-CSRF-TOKEN': $(
										'#_csrf').attr(
											'content'),
								},
								type: "POST",
								data: {
									encryptedId: txnId1
								},
								success: function(result) {

									if (result != null) {

										$("#exampleModal2")
											.modal(
												'show');

										var html = "";
										$.each(result, (index, row) => {

											var referenceTxnID = (row.referenceTxnID != null && row.referenceTxnID != undefined) ? row.referenceTxnID : "";
											var rrn = (row.rrn != null && row.rrn != undefined) ? row.rrn : "";
											var terminalId = (row.terminalId != null && row.terminalId != undefined) ? row.terminalId : "";
											var maskedCardNumber = (row.maskedCardNumber != null && row.maskedCardNumber != undefined) ? row.maskedCardNumber : "";
											var authCode = (row.authCode != null && row.authCode != undefined) ? row.authCode : "";



											html += '<tr>' + '<td>' + referenceTxnID + '</td>' + '<td>' + row.transactionTypeName + '</td>' + '<td>' + terminalId + '</td>' + '<td>' + row.userName + '</td>' + '<td>' + maskedCardNumber + '</td>' + '<td>' + rrn + '</td>' + '<td>' + row.amount + '</td>' + '<td>' + row.date + '</td>' + '<td>' + authCode + '</td>' + '<td>' + row.transactionStatus + '</td>' + '</tr>';


										});

										$('#tbdata').append(html);

									}

								},


								error: function(xhr,
									error, code) {

									if (error === 'parsererror') {
										window.location.href = +contexturi
											+ "/login?invalid";
									}

								}
							});

					});

			$("#transactionDateFrom").datepicker({
				format: 'dd-mm-yyyy',
				autoclose: true,
				endDate: '+d'
			});

			$("#transactionDateTo").datepicker({
				format: 'dd-mm-yyyy',
				autoclose: true,
				endDate: '+d'
			});

			var token = $('#_csrf').attr('content');
			var header = $('#_csrf_header').attr('content');
			var i = 1;
			var table = $('#table_id')
				.DataTable(

					{
						"processing": true,
						"serverSide": true,
						// "scrollY": $(document).height() -
						// 400,
						"scrollCollapse": true,
						"pageLength": 100,
						"paging": true,
						"dom": "lBrtip",
						"buttons": [
							{
								extend: 'excel',
								exportOptions: {
									columns: [1, 2, 3, 4,
										5, 6, 7, 8, 9,
										10, 11, 12, 13]
								},

							},

						],
						initComplete: function() {

							$('.buttons-excel')
								.html(
									'<i class="far fa-arrow-alt-circle-down" /> Excel')

						},
						"order": [[0, "desc"]],
						"ajax": {
							"headers": {

							},
							"url": contexturi
								+ "/transaction/get-transaction-list-data",
							"contentType": "application/json",
							"type": "POST",
							"timeout": "6000000",
							"data": function(d) {

								var postdata = {};
								postdata.dtRequest = d;
								postdata.userId = $("#userId")
									.val();
								postdata.merchantName = $(
									"#merchantName").val();
								postdata.transactionId = $(
									"#transactionId").val();
								postdata.rrn = $('#rrn').val();
								postdata.authCode = $(
									"#authCode").val();
								postdata.maskedCardNumber = $(
									"#maskedCardNumber")
									.val();
								postdata.cardHolderName = $(
									"#cardHolderName")
									.val();
								postdata.acquirer = $(
									"#acquirer").val();
								postdata.mid = $("#mid").val();
								postdata.tid = $("#tid").val();
								postdata.txnType = $(
									'input[name="txnType"]:checked')
									.val();
								postdata.transactionDateFrom = $(
									"#transactionDateFrom")
									.val();
								postdata.transactionDateTo = $(
									"#transactionDateTo")
									.val();
								postdata.acquirerCheck = $(
									"#acquirerCheck").val();
								postdata.dbaname = $(
									"#dbaname").val();
								postdata.programType = $("#programType").val();
								postdata.transactionTime = $("#transactionTime").val();
								postdata.posId = $("#posId").val();
								postdata.merchantDate = $("#merchantDate").val();


								return JSON.stringify(postdata);
							},
							"beforeSend": function(request) {
								$(".validationAlert").text("");
								$("#search").attr('disabled',
									true);
								request.setRequestHeader(
									header, token);
							},
							"complete": function(settings,
								json) {
								$("#search").attr('disabled',
									false);
							},
							"dataSrc": function(json) {
								if (json.errors != null) {
									if (json.errors.userId !== undefined) {
										$("#userId_alert")
											.text(
												json.errors.userId);
									}

									if (json.errors.merchantName !== undefined) {
										$("#merchantName_alert")
											.text(
												json.errors.merchantName);
									}

									if (json.errors.transactionId !== undefined) {
										$(
											"#transactionId_alert")
											.text(
												json.errors.transactionId);
									}

									if (json.errors.rrn !== undefined) {
										$("#rrn_alert")
											.text(
												json.errors.rrn);
									}

									if (json.errors.authCode !== undefined) {
										$("#authCode_alert")
											.text(
												json.errors.authCode);
									}

									if (json.errors.maskedCardNumber !== undefined) {
										$(
											"#maskedCardNumber_alert")
											.text(
												json.errors.maskedCardNumber);
									}

									if (json.errors.cardHolderName !== undefined) {
										$(
											"#cardHolderName_alert")
											.text(
												json.errors.cardHolderName);
									}

									if (json.errors.acquirer !== undefined) {
										$("#acquirer_alert")
											.text(
												json.errors.acquirer);
									}

									if (json.errors.mid !== undefined) {
										$("#mid_alert")
											.text(
												json.errors.mid);
									}

									if (json.errors.tid !== undefined) {
										$("#tid_alert")
											.text(
												json.errors.tid);
									}

									if (json.errors.txnType !== undefined) {
										$("#txnType_alert")
											.text(
												json.errors.txnType);
									}

									if (json.errors.transactionDateFrom !== undefined) {
										$(
											"#transactionDateFrom_alert")
											.text(
												json.errors.transactionDateFrom);
									}

									if (json.errors.transactionDateTo !== undefined) {
										$(
											"#transactionDateTo_alert")
											.text(
												json.errors.transactionDateTo);
									}
								}
								return json.data;
							},
							"error": function(xhr, error, code) {

								if (error === 'parsererror'
									|| error === 'timeout') {
									window.location.href = contexturi
										+ "/login?invalid";
								}
							}
						},


						"fnRowCallback": function(nRow, aData,
							iDisplayIndex) {

							if (aData.isRefund != 0) {
								//cell background color
								$(nRow).find('td').css('color', 'red');
							}

							$("td:first", nRow).html(
								aData.DT_RowIndex);
							return nRow;
						},
						"columns": [
							{
								"title": "Sr.No",
								render: function(data,
									type, row, meta) {
									return meta.row
										+ meta.settings._iDisplayStart
										+ 1;
								}
							},

							{
								"data": "transactionId"
							},
							{
								"data": "type"
							},
							{
								"data": "terminalId"
							},
							{
								"data": 'userName'
							},
							{
								"data": 'cardNumber'
							},
							{
								"data": 'rrn'
							},
							{
								"data": "amt",
							},
							{
								"data": "txnDate",
								"render": function(data,
									type, row, meta) {
									var newDate = data
										.split(" ");
									if (newDate[0] !== undefined) {
										return newDate[0];
									} else {
										return "";
									}
								}
							},
							{
								"data": "txnDate",
								"render": function(data,
									type, row, meta) {
									var newDate = data
										.split(" ");
									/*
									 * if(newDate[1] !==
									 * undefined){ return
									 * newDate[1]; }else{
									 * return ""; }
									 */
									return formatAMPM(new Date(
										data));
								}
							},
							{
								"data": 'responseCode',
								"render": function(data,
									type, row, meta) {

									data = '<a href="javascript:void(0)" data-name='
										+ row.responseCode
										+ ' class="responsealert" data-tgId="'
										+ row.tgId
										+ '" ">'
										+ row.responseCode
										+ '</a>';
									return data;
								}
							},
							{
								"data": 'authCode'
							},
							{
								"data": 'status'
							},
							{
								"data": "settleStatus",
								"render": function(data,
									type, row, meta) {
									var acqName = row.acuirerName;
									var transactionTypeId = row.transactionTypeId;
									if ((acqName == 'PAYTM' || acqName == 'MOSAMBEEPAYTM')
										&& data == 1
										&& transactionTypeId == 1) {
										return '<div id="unsettled'
											+ row.transactionId
											+ '"><button type="button" class="btn btn-xs btn-primary unsettled" data-transactionid="'
											+ row.transactionId
											+ '" title="Un-Settled" >Un-Settled</button>';

									}

									if (transactionTypeId == '21' || transactionTypeId == '19' || transactionTypeId == '22' || transactionTypeId == '24') {
										return 'NA';
									}
									else {

										if (data == 1) {
											return 'Un-Settled';
										} else if (data == 4) {
											return 'NA';
										} else {
											return 'Settled';
										}
									}

								}
							},
							{
								"data": 'isVoided'
							},
							{
								"data": 'isSubvented'
							},
							{
								"data": "transactionId", // "data":
								// "transactionId"
								"render": function(data,
									type, row, meta) {

									return '<button type="button" class="btn btn-xs btn-primary showTransactionList" data-transactionid="'
										+ row.transactionId
										+ '" title="View"  ><i class="fa fa-eye " ></i></button>';


								}

							},

							{
								"data": "encryptedId",
								"render": function(data,
									type, row, meta) {



									return '<button type="button" class="btn btn-xs btn-primary showTransactionList1" data-transactionid="'
										+ row.encryptedId
										+ '" title="View"  ><i class="fa fa-envelope " ></i></button>';

								}
							},

							{
								"data": "encryptedId",
								"render": function(data,
									type, row, meta) {

									var refundstatus = row.isRefund;

									var text = row.type

									if (text != null) {

										var result = text.match(/Refund/g);
										var output1 = Boolean(result)
									}
									if (refundstatus != 0 && output1 != true) {
										if ($("#commonDb")
											.val() == 1) {

											return '<button type="button" class="btn btn-xs btn-primary showTransactionList2" data-transactionid="'
												+ row.mposTxnId
												+ '" title="View"  ><i class="fa fa-download " ></i></button>';
										} else {
											return '<button type="button" class="btn btn-xs btn-primary showTransactionList2" data-transactionid="'
												+ row.encryptedId
												+ '" title="View"  ><i class="fa fa-eye " ></i></button>';
										}
									}

									else {
										return '<button type="button" disabled="true" class="btn btn-xs btn-primary showTransactionList2 data-transactionid="'
											+ row.encryptedId
											+ '" title="View"  ><i class="fa fa-eye " ></i></button>';

									}
								},



							},],

					});


			$('#table_id')
				.on(
					"click",
					".unsettled",
					function() {
						var id = $(this).data('transactionid');
						var data = table.row(
							$(this).parents("tr")).data();
						var transactionid1 = data['transactionId'];


						$
							.ajax({
								url: contexturi
									+ "/transaction/change_settlement-status ",
								headers: {
									'X-CSRF-TOKEN': $(
										'#_csrf').attr(
											'content'),
								},
								type: "POST",
								data: {
									tranId: transactionid1
								},
								beforeSend: function() {
									$("#sendSms")
										.val('Sending')
										.attr(
											'disabled',
											true);
								},
								success: function(data) {

									if (data == 'success') {
										$("#unsettled" + id)
											.text(
												"Settled");
										$("#success")
											.show();
									} else {
										$("#failed").show();
									}
								},
								error: function(xhr,
									error, code) {

								}
							});

					});

			// Apply the search
			/*table
				.columns()
				.every(
					function() {
						var columns = this;

						$('input', this.footer()).not( "#sms , #send_email")
							.on(
								' change clear',
								function() {
									if (columns
										.search() !== this.value) {
										columns
											.search(
												this.value)
											.draw();
									}
								});
					})*/

			$("#refund-history").on("click", function(event) {
				var encryptedId1 = txnId1

				var link = $("#receiptUrl").val()
				var jsonData = {
					"encryptedId": encryptedId1

				}



				$.ajax({
					url: link + "/summaryreport/refund/refund-transaction-download",

					headers: {
						'X-CSRF-TOKEN': $('#_csrf').attr('content'),
					},
					type: "POST",
					xhrFields: {
						responseType: 'blob'
					},
					contentType: "application/json",
					data: JSON.stringify(jsonData),
					success: function(result) {

						var blob = result;
						var downloadUrl = URL.createObjectURL(blob);
						var a = document.createElement("a");
						a.href = downloadUrl;
						a.download = "Refundtransaction.xlsx";
						document.body.appendChild(a);
						a.click();

					}


				});

			});



			$(document).on(
				'click',

				'.responsealert',
				function() {

					var i = this
					var data = table.row($(this).parents("tr"))
						.data();

					var tgId = data['tgId'];
					var response = data['responseCode'];
					$.ajax({
						url: contexturi
							+ "/user/getResponseDescription",
						headers: {
							'X-CSRF-TOKEN': $('#_csrf').attr(
								'content'),
						},
						type: "POST",
						data: {
							tgId: tgId,
							response: response
						},
						success: function(result) {
							if (/\bDOCTYPE\b/i.test(result)) {
								window.location.href = +contexturi
									+ "/login?invalid";
								return false

							}
							var res = result;
							res = res.split("|");
							var desc = res[0];
							var action = res[1];
							$(i).popover({
								html: true,
								placement: 'right',
								trigger: 'focus',
								content: desc + "<br>" + action
							}).popover('show')
						},




						error: function(xhr, error, code) {
							if (error === 'parsererror') {
								window.location.href = +contexturi
									+ "/login?invalid";
							}
						}
					});
				});

		});

$("#send_email,#sms,#sendEmail,#sendSms").hide();

$(".smsClick").click(function() {
	$("#send_email").val("");
	$(".validationAlert").text();
	$("#sms").show();
	$("#sendSms").show();
	$("#send_email").hide();
	$("#sendEmail").hide();
	$("#email_alert").hide();
	$("#sms_alert").show();



});

$(".emailClick").click(function() {
	$("#sms").val("");
	$(".validationAlert").text();
	$("#send_email").show();
	$("#sms").hide();
	$("#sendEmail").show();
	$("#sendSms").hide();
	$("#email_alert").show();
	$("#sms_alert").hide();
});



$(document).on('click', '#sendEmail', function(e) {

	e.preventDefault();

	$(".validationAlert").text("");

	var emailRegex = /[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,8}/igm;

	var link = $("#receiptUrl").val()

	if ($.trim($("#send_email").val()) == "") {
		$("#email_alert").text("Please enter email");
		return false;
	}

	if ($.trim($("#send_email").val()) != "") {
		if (!emailRegex.test($.trim($("#send_email").val()))) {
			$("#email_alert").text("Please enter valid email");
			return false;
		}
	}

	$.ajax({
		url: link + "/transaction/send-email-sms",
		headers: {
			'X-CSRF-TOKEN': $('#_csrf').attr('content'),
		},
		type: "POST",
		data: { transactionId: $("#tranId").val(), email: $("#send_email").val(), mobile: $("#sms").val() },
		beforeSend: function() {
			$("#sendEmail").val('Sending').attr('disabled', true);
		},
		async: true,
		success: function(result) {
			$("#sendEmail").val('Submit').attr('disabled', false);
			$("#exampleModal3").modal('hide');
			if (result) {
				$("#emailSuccess").show();
			} else {
				$("#emailFail").show();
			}
			$("html, body").animate({ scrollTop: 0 }, "fast");
			setTimeout(function() { $("#emailSuccess,#emailFail").hide(); }, 3000);

		},
		error: function(xhr, error, code) {
			$("#sendEmail").val('Submit').attr('disabled', false);
			if (error === 'parsererror') {
				window.location.href = +contexturi + "/login?invalid";
			}

		}
	});

	e.stopImmediatePropagation();
	return false;
});




$(document).on('click', '#sendSms', function(e) {

	e.preventDefault();

	$(".validationAlert").text("");


	var mobileRegex = /^[0-9]{8,12}$/
	var link = $("#receiptUrl").val()


	if ($.trim($("#sms").val()) == "") {
		$("#sms_alert").text("Please enter mobile number");
		return false;
	}

	if ($.trim($("#sms").val()) != "") {
		if (!mobileRegex.test($.trim($("#sms").val()))) {
			$("#sms_alert").text("Please enter valid  mobile number");
			return false;
		}
	}

	$.ajax({
		url: link + "/transaction/send-email-sms",
		headers: {
			'X-CSRF-TOKEN': $('#_csrf').attr('content'),
		},
		type: "POST",
		data: { transactionId: $("#tranId").val(), mobile: $("#sms").val(), email: $("#send_email").val() },
		beforeSend: function() {
			$("#sendSms").val('Sending').attr('disabled', true);
		},
		async: true,
		success: function(result) {
			$("#sendSms").val('Submit').attr('disabled', false);
			$("#exampleModal3").modal('hide');
			if (result) {
				$("#smsmSuccess").show();
			} else {
				$("#smsFail").show();
			}
			$("html, body").animate({ scrollTop: 0 }, "fast");
			setTimeout(function() { $("#smsmSuccess,#smsFail").hide(); }, 9000);
		},
		error: function(xhr, error, code) {
			$("#sendSms").val('Submit').attr('disabled', false);
			if (error === 'parsererror') {
				window.location.href = +contexturi + "/login?invalid";
			}

		}


	});
	e.stopImmediatePropagation();
	return false;
});

$('.responsealert').click(function() {
	$('.popover:visible').popover('destroy')
});

function parseDate(str) {
	var mdy = str.split('-');
	return new Date(mdy[2], mdy[0] - 1, mdy[1]);
}

function datediff(first, second) {
	// Take the difference between the dates and divide by milliseconds per day.
	// Round to nearest whole number to deal with DST.
	return Math.round((second - first) / (1000 * 60 * 60 * 24));
}

function formatAMPM(date) {
	var hours = date.getHours();
	var minutes = date.getMinutes();
	var ampm = hours >= 12 ? 'pm' : 'am';
	hours = hours % 12;
	hours = hours ? hours : 12; // the hour '0' should be '12'
	minutes = minutes < 10 ? '0' + minutes : minutes;
	var strTime = hours + ':' + minutes + ' ' + ampm;
	return strTime;
}