$(document).ready(function () {
	$('#card-alerts').show();
	$("#card-alerts").alert();
	window.setTimeout(function () {
		$("#card-alerts").hide();
	}, 2000);
	
	$("#startTime").datepicker({
		format: 'dd-mm-yyyy',
		autoclose: true,
		endDate:'+d'
	});
	
	$("#endTime").datepicker({
		format: 'dd-mm-yyyy',
		autoclose: true,
		endDate:'+d'
	});
	

	
    // DataTable
    var token = $('#_csrf').attr('content');
    var header = $('#_csrf_header').attr('content');
    // getting contextPath
    var myContextPath=$("#app_context_path").attr('content') ;
    
    $(document).on( "click", ".userstatusChange",function() {
	    var status = ($(this).is(':checked')==true)?1:0;        
	    var groupId = $(this).data("id");
		$("#userStatus").val(status);
		$("#userId").val(groupId);
		$("#statusId").val(this.id);
		$("#userStatusModal").modal('show');       
    });


    var table = $('#table_id').DataTable({
        "processing": true,
        "serverSide": true,
        //"scrollY": $(document).height() - 400,
        "scrollCollapse": true,
        "paging": true,
        "createdRow" : function(row, data,
				index) {
			var info = table.page.info();
			$('td', row).eq(0).html(
					index + 1 + info.page
							* info.length);
		},
        "dom":
        	"<'row'<'col-sm-12 col-md-12'l>>" +
			"<'row'<'col-sm-12'tr>>" +
			"<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "ajax": {
            "headers": {

            },
            "url": myContextPath + "/settlement/get-settlement-data",
            "contentType": "application/json",
            "type": "POST",
            "timeout":"60000",
            "data": function (d) {
            	var startTime="";
            	var endTime="";
            	if($.trim($("#startTime").val()) != ""){
            		let d = $("#startTime").val().split("-");
            		startTime = d[2]+"-"+d[1]+"-"+d[0];
            	}
            	
            	if($.trim($("#endTime").val()) != ""){
            		let e = $("#endTime").val().split("-");
            		endTime = e[2]+"-"+e[1]+"-"+e[0];
            	}
            	
            	var postdata ={};
            	postdata.dtRequest = d;
				postdata.settlementType = $('#settlementType').val();
				postdata.settlementTypeSearch = $("#settlementTypeSearch").val();
				postdata.firstName = $('#firstName').val();
				postdata.type = "";
				postdata.startTime = startTime;
				postdata.endTime = endTime;
				postdata.reason = $('#reason').val();
                return JSON.stringify(postdata);
            },
            "beforeSend": function (request) {
            	$("#search").attr('disabled',true);
                request.setRequestHeader(header, token);
            },
            "complete": function( settings, json ) {
            	$("#search").attr('disabled',false);
            },
            "dataSrc" : function(json){

                if(json.errors != null){	
    	          	if(json.errors.settlementTypeSearch !== undefined){
    	          		$("#settlementType_error").text(json.errors.settlementTypeSearch);
    	          	}
    	          	
    	          	if(json.errors.firstName !== undefined){
    	          		$("#firstName_error").text(json.errors.firstName);
    	          	}
    	          	
    	          	if(json.errors.startTime !== undefined){
    	          		$("#startTime_error").text(json.errors.startTime);
    	          	}
    	          	
    	          	if(json.errors.endTime !== undefined){
    	          		$("#endTime_error").text(json.errors.endTime);
    	          	}
    	          	
    	          	if(json.errors.reason !== undefined){
    	          		$("#reason_error").text(json.errors.reason);
    	          	}
              	
                }
              	return json.data;
              },
            "error": function (xhr, error, code) {

                if (error === 'parsererror' || error === 'timeout') {
                    window.location.href = myContextPath + "/login?invalid";
                }
            }
        },

        "columns": [
        	{"data":"serialNo"},
        	{ "data": "settlementFor"},
            { "data": 'initiatedBy' },
            { "data": 'type' },
            { "data": 'startTime' },
            { "data": 'endTime' },
            { "data": 'reason' },
        ]
    });

    // Apply the search
    table.columns().every(function () {
        var that = this;

        $('input', this.footer()).on('keyup change clear', function () {
            if (that.search() !== this.value) {
                that
                    .search(this.value)
                    .draw();
            }
        });
    });
});

$("#search").on("click", function (event) {
	event.preventDefault();
	$(".validationAlert").text("");
	let merRegex = /^[a-zA-Z0-9 ]+$/;
	let nameRegex = /^[A-Za-z ]+$/;
	let numRegex = /^[0-9]+$/;
	
	
         if($("#settlementType").val() == "merchant"){
    	     if($("#settlementTypeSearch").val() != ""){
	        	if($("#settlementTypeSearch").val().length >  20){
	        		$("#settlementType_error").text("Please enter valid merchant");
	        		return false;
	        	}
	        	if(!merRegex.test($("#settlementTypeSearch").val())){
	        		$("#settlementType_error").text("Please enter valid merchant");
	        		return false;
	        	}
    	     }
    	 }else  if($("#settlementType").val() == "user"){
    		 if($("#settlementTypeSearch").val() != ""){
	        	if($("#settlementTypeSearch").val().length >  10){
	        		$("#settlementType_error").text("Please enter valid user");
	        		return false;
	        	}
	        	
	        	if(!merRegex.test($("#settlementTypeSearch").val())){
	        		$("#settlementType_error").text("Please enter valid user");
	        		return false;
	        	}
    		 }
    	}else  if($("#settlementType").val() == "time"){
    		 if($("#settlementTypeSearch").val() != ""){
	        	if($("#settlementTypeSearch").val().length >  2){
	        		$("#settlementType_error").text("Please enter valid time");
	        		return false;
	        	}
	        	
	        	if(!numRegex.test($("#settlementTypeSearch").val())){
	        		$("#settlementType_error").text("Please enter valid time");
	        		return false;
	        	}
    		 }
    	}else{
    		 if($("#settlementType").val() != ""){
	    		if($("#settlementTypeSearch").val().length >  25){
	        		$("#settlementType_error").text("Please enter valid auto");
	        		return false;
	        	}
    		 }
    	}
         
         
        if($("#firstName").val() !=  ""){
        	if($("#firstName").val().length > 25){
        		$("#firstName_error").text("Please enter valid Initiated by");
        		return false;
        	}
        	
        	if(!nameRegex.test($("#firstName").val())){
        		$("#firstName_error").text("Please enter valid Initiated by");
        		return false;
        	}
        	
        }
        
        if($("#reason").val() !=  ""){
        	if($("#reason").val().length > 20){
        		$("#reason_error").text("Please enter valid reason");
        		return false;
        	}
        	
        	if(!nameRegex.test($("#reason").val())){
        		$("#reason_error").text("Please enter valid reason");
        		return false;
        	}
        	
        }
	
	$('#table_id').dataTable().fnFilter();
});


















