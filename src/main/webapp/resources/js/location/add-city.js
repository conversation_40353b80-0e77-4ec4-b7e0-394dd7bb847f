$(document).ready(
		function() {

			$.validator.addMethod("regex", function(value, element, regexp) {
				return this.optional(element) || regexp.test(value);
			}, "Please enter alphabets only.");

			$("form[name='addCityBean']").validate({
				errorClass : 'validatorError',
				rules : {
					cityName : {
						required : true,
						maxlength : 100,
						regex : /^[a-zA-Z\s,-]+$/
					},
					cityCode : {
						required : true,
						maxlength : 2,
						regex : /^[a-zA-Z\s,-]+$/
					}
				},
				// Specify validation error messages
				messages : {
					cityName : {
						required : "Please enter city name",
						maxlength : "Only 100 letters allowed"
					},
					cityCode : {
						required : "Please enter city code",
						maxlength : "Only 2 digits allowed"
					}
				},
				// Make sure the form is submitted to the destination defined
				// in the "action" attribute of the form when valid
				submitHandler : function(form) {
					form.submit();
				}
			});

			$("#submitBtn")
					.click(
							function(e) {
								e.preventDefault();
								$(".validationAlert").text("");
								if ($.trim($("#countryId").val()) == "0") {
									$("#countryId_error").text(
											"Please select country");
									return false;
								}

								if ($.trim($("#stateId").val()) == "0"
										|| $.trim($("#stateId").val()) == "") {
									$("#stateId_error").text(
											"Please select state");
									return false;
								}

								$("#addCityBean").submit();
							});

			$(document).on(
					'change',
					'#countryId',
					function() {

						$.ajax({
							url : contexturi + "/location/get-states",
							headers : {
								'X-CSRF-TOKEN' : $('#_csrf').attr('content'),
							},
							type : "POST",
							data : {
								countryId : $(this).val()
							},
							success : function(result) {
								var countryHtml = '';
								$.each(result, function(index, value) {

									countryHtml += '<option value="' + index
											+ '">' + value + '</option>';
								});

								if ($.trim($("#state_data").val()) != "") {
									$("#stateId").val($("#state_data").val());
								}

								$("#stateId").html(countryHtml);
							},
							error : function(xhr, error, code) {
								if (error === 'parsererror'
										|| error === 'timeout') {
									window.location.href = +contexturi
											+ "/login?invalid";
								}
							}
						});
					});

			$('#card-alerts').show();
			$("#card-alerts").alert();
			window.setTimeout(function() {
				$("#card-alerts").hide();
			}, 2000);
		});