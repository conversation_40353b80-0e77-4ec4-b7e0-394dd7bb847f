$(document).ready(function() {

	jQuery.validator.addMethod("AllLetter", function(value, element) {
		return this.optional(element) || /^[ A-Z a-z]*$/.test(value);
	}, 'Please Enter Only Alphabets');

	jQuery("#addCountryDetails").validate({
		onClick : false,
		rules : {
			countryName : {
				required : true,
				maxlength : 100,
				AllLetter : true
			},
			countryCode : {
				required : true,
				maxlength : 4,
				AllLetter : true
			}
		},
		errorPlacement : function(error, element) {
			error.insertAfter(element);
		}
	});

	$('#card-alerts').show();
	$("#card-alerts").alert();
	window.setTimeout(function() {
		$("#card-alerts").hide();
	}, 2000);
});