$(document).ready(function () {
	
	
	$('#card-alerts').show();
	$("#card-alerts").alert();
    
	//time for alert error message to be shown
	setTimeout(function () {
		$(".alert").alert('close');
	}, 4000);
	
	
    // DataTable
    var token = $('#_csrf').attr('content');
    var header = $('#_csrf_header').attr('content');
    var postdata = {};
    
    // Data Table ajax call and storing the value in table.
    var table = $('#city-table-id').DataTable({
        "processing": true,
        "serverSide": true,
        "scrollCollapse": true,
        "paging": true,
        "createdRow": function (row, data, index) {
            var info = table.page.info();
            $('td', row).eq(0).html(index + 1 + info.page * info.length);
        },
        "dom": 
        	"<'row'<'col-sm-12 col-md-12'l>>" +
			"<'row'<'col-sm-12'tr>>" +
			"<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        
        "ajax": {
            "url": contexturi + "/location-list/city-list",
            "contentType": "application/json",
            "type": "POST",
            "data": function (d) {
            	
            	 postdata.dataTablesParameter = d;
            	 postdata.stateId = $('#stateId').val();
            	 postdata.cityName = $('#cityName').val();
            	 
                return JSON.stringify(postdata);
            },
            "beforeSend": function (request) {
                request.setRequestHeader(header, token);
            },
            "error": function (xhr, error, code) {

                if (error === 'parsererror' || error === 'timeout' || error === 'error') {
                    window.location.href = myContextPath + "/login?invalid";
                }
            }
        },
        
        "columns": [
        	{ "data": 'srNo'},
            { "data": 'name' },
            { "data": 'code' },
            
            
        ],
        "order": [[0, "desc"]]
    });
    
   
    // getting contextPath
	var myContextPath=$("#app_context_path").attr('content') ;
   
    
 // search function
    $("#search").on("click", function () {
    	
        //$("#dataTableBody").show();
        $('#city-table-id').dataTable().fnFilter();
    	
    });
    
 // download function

	$("#download").on("click", function() {
	$("#cityNameForDownload").val($("#cityName").val());
	$("#stateIdForDownload").val($("#stateId").val());
	$("#downloadForm2").submit();

	});
	// end of download page
    
    // For acquirer dropdown
	$('.select2').select2();
    
});
