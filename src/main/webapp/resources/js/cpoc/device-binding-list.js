$(document).ready(function() 
		{
					
					// No special char control handler
					function isValid(str) {
						return !/[~`!@#$%\^&*()+=\-\[\]\\'.;,/{}|\\":<>\?]/g
								.test(str);
					}

					// to hide the datatable default
					//$("#binding_table").hide();

					// DataTable
					var token = $('#_csrf').attr('content');
					var header = $('#_csrf_header').attr('content');
					// getting contextPath
					var myContextPath = $("#app_context_path").attr('content');

					// calling datatable request function
					var postdata = {};
				

				
					// to fetch the enquiry reporting list
					var table = $('#binding-table-id')
							.DataTable(
									{
										"processing" : true,
										"serverSide" : true,
										"scrollCollapse" : true,
										"paging" : true,
										"createdRow" : function(row, data,
												index) {
											var info = table.page.info();
											$('td', row).eq(0).html(
													index + 1 + info.page
															* info.length);
										},
										"dom" : "<'row'<'col-sm-12 col-md-12'l>>"
												+ "<'row'<'col-sm-12'tr>>"
												+ "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
										"ajax" : {
											"url" : myContextPath + "/auditlogs-reporting/device-binding-list",
											"contentType" : "application/json",
											"type" : "POST",
											"timeout":"60000",
											"data" : function(d) {

												postdata.dtRequest = d;
									
												postdata.userName = $('#userName').val();
												postdata.deviceId = $('#deviceId').val();
												postdata.bindingType = $('#bindingType').val();
												return JSON.stringify(postdata);

											},
											"beforeSend" : function(request) {
												request.setRequestHeader(header, token);
											},
											"error" : function(xhr, error, code) {

												if (error === 'parsererror' || error === 'timeout' || error === 'error') {
													window.location.href = myContextPath
															+ "/login?invalid";
												}
											}
										},
										"columns" : [
											
											{
												"data" : 'srNo',
												orderable:false,
											},

												{
													"data" : 'userName',
													orderable:false,
												},
												{
													"data" : 'deviceId',
													orderable:false,
												},
												
												{
									            	"data": "bindingType",
									            	"render": function (data, type, row, meta) {
									            		if(row.bindingType=='0'){
									            			return '<p>Untie Device</p>';
										            	}else{
										            		return '<p>Binding</p>';
										            	}
									            	}
								            },
								
												{
								            	
								            	"data": "id",
								            	"render": function (data, type, row, meta) {
								            		if(row.bindingType=='0'){
								            			//alert(row.id);
									            		return '<button type="button" title="View" class="btn btn-primary btn-xs mx-auto d-block disabled"  ><i class="fa fa-eye" ></i></button>';
									            	}else{
									            		return '<button type="button" title="View" class="btn btn-primary btn-xs mx-auto d-block userstatusChange" data-toggle="modal" data-target="#modal-default" id="'+row.id+'" ><i class="fa fa-eye" ></i></button>';
									            	}
								            	}
													//"defaultContent" : '<button type="button" title="View" class="btn btn-primary btn-xs mx-auto d-block" data-toggle="modal" data-target="#modal-default" id="view"><i class="fa fa-eye" ></i></button>'
												},

										],
										"order" : [ [ 0, "desc" ] ]
									});
					$(document).on("click", ".userstatusChange", function() {
					
						var groupId = this.id;
						$("#userId").val(this.id);
				
					});
					$(document).on("click", "#confirmBlock", function() {
					var id=$("#userId").val();
						//$("#statusId").val(this.id);
					 updateUserStatus('confirm');

						$("#userStatusModal").modal('show');
					});
				
					// on search reload the form
					$("#search").on("click", function(event) {
			
							$("#userName_error").html("");
							$("#deviceId_error").html("");
							$(".validationALert").html("");
							// if todate is null then set one month
							// back date
							var numbers = /^[-+]?[0-9]+$/;
							if ($.trim($("#userName").val()) != '') {

								if ($("#userName").val().length > 50 || $("#userName").val().length < 8) {
									$("#userName_error").html("Please enter valid length(minlength=8 and maxlength=50)");
									return false;
								}
							}
							if($("#deviceId").val() !=""){
								
								if (!$("#deviceId").val().match(numbers)) {
									$("#deviceId_error").html("Only numbers are allowed");
									return false;
								}

								if ($.trim($("#deviceId").val()) != '') {

									if ($("#deviceId").val().length > 20	|| $("#deviceId").val().length < 4) {
										$("#deviceId_error").html("Please enter valid length(minlength=4 and maxlength=20)");
										return false;
									}
																	
								}
							}
							
							//to again reload/resubmit the form by calling the dataTableFilter
							$("#binding_table").show();
							$('#binding-table-id').dataTable().fnFilter();
							
						});

					/*$("#confirmBlock").click(function() {
						updateUserStatus('confirm');
					});*/

					$("#confirmClose").click(function() {

						$("#modal-default").modal('hide');
					});
					function updateUserStatus(action) {

						if (action == 'close') {
							return false;
						}
		
						$("#cover-spin").show();
						$.ajax({
							url : contexturi + "/auditlogs-reporting/update-status",
							headers : {
								'X-CSRF-TOKEN' : $('#_csrf').attr('content'),
							},
							type : "POST",
							data : {
								id :$("#userId").val(),
							
							},
							success : function(result) {

								checkUpdate = true;
								$("#cover-spin").hide();
								$("#userStatusModal").modal('hide');
								$("#comment").val('');
								if (result == true) {
									window.scrollTo(0, 0);
									$("#userSuccess").show();
								} else {
									window.scrollTo(0, 0);
									$("#userFailed").show();
								}
								setTimeout(function() {
									$(".alert").alert('close');
								}, 4000);
								setTimeout(function() {
									location.reload();
								}, 4000);

							},
							error : function(xhr, error, code) {

								if (error === 'parsererror') {
									window.location.href = +contexturi + "/login?invalid";
								}

							}
						});

					}

					// calling special character function
					$(".no-special-char").keypress(function(event) {
						var character = String.fromCharCode(event.keyCode);
						return isValid(character);
					});

					

					// For issuer dropdown
					$('.select2').select2();

				});

