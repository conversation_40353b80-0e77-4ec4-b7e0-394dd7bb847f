$(document).ready(function () {
	$("#reloadPage").hide();
	// THIS BLOCK IS RESPONSIBLE FOR DISPLAYING THE FILE NAME AFTER PICKING THE
	// FILE.
	$('.custom-file-input').on('change', function () {
		let fileName = $(this).val().split('\\').pop();
		if(fileName!=''){
			$(this).next('.custom-file-label').addClass("selected").html(fileName);
		}
//		$(this).next('.custom-file-label').addClass("selected").html(fileName);
	});

	// REMOVING ALERT AFTER DELAY OF 4 SECONDS.
	setTimeout(function () {
		$(".alert").alert('close');
	}, 4000);
//});

//  
$("#submit").click(function(e){
	
e.preventDefault();
if(validateExcelFile()){
	
	$("#submitBlock").remove();
	$("#submitInput").hide();
	$("#reloadPage").css('display','block');
	setTimeout(function(){ $("#program-bulk-upload").submit(); }, 500);
}
});

$("#reloadPage").click(function(){
window.location.reload();
});
});


function validateExcelFile(){
	 var result = true;
	 $(".validationAlert").text("");
     
	 if($.trim($("#file").val()) == ""){
		 
		 $("#file_alert").text("Please upload xlsx file");
		 result = false;
	 }
     
	 if($.trim($("#file").val()) != ""){
		 var ext = $('#file').val().split('.').pop().toLowerCase();

		 if(ext != 'xlsx'){
		    $("#file_alert").text("Please upload valid xlsx file");
		    result = false;
		 }
		 
		 if($("#file")[0].files[0].size > 2097152 ){
			    $("#file_alert").text("max file size is 2mb");
			    result = false;
	     }
	 }

	 return result;

}
	

		