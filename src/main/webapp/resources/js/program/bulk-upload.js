$(document).ready(function () {

	// THIS BLOCK IS RESPONSIBLE FOR DISPLAYING THE FILE NAME AFTER PICKING THE FILE.
	$('.custom-file-input').on('change', function () {
		let fileName = $(this).val().split('\\').pop();
//		$(this).next('.custom-file-label').addClass("selected").html(fileName);
		if(fileName!=''){
			$(this).next('.custom-file-label').addClass("selected").html(fileName);
		}
	});

	// REMOVING ALERT AFTER DELAY OF 4 SECONDS.
	setTimeout(function () {
		$(".alert").alert('close');
	}, 4000);

});
//checking if chosen file is empty 
function validateFile() {

	$("#validationAlert").html("");
	var file = $('#inputGroupFile01').val();
	var result = true;
	if (!(/\.(xlsx|xls|xlsm)$/i).test(file)) {
		$("#validationAlert").html('Please upload valid .xlsx excel file');
		$(file).val('');
		result = false;
	}

	return result;
}