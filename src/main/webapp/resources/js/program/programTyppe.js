$(document).ready(function () {

	// THIS BLOCK IS RESPONSIBLE FOR DISPLAYING THE FILE NAME AFTER PICKING THE
	// FILE.
	$('.custom-file-input').on('change', function () {
		let fileName = $(this).val().split('\\').pop();
		if(fileName!=''){
			$(this).next('.custom-file-label').addClass("selected").html(fileName);
		}
//		$(this).next('.custom-file-label').addClass("selected").html(fileName);
	});

	// REMOVING ALERT AFTER DELAY OF 4 SECONDS.
	setTimeout(function () {
		$(".alert").alert('close');
	}, 4000);
	// RE-UPLOAD button implementation 
	$("#reloadPage").hide();
	$("#submitUploadMerchant").click(function(e){
	e.preventDefault();
	if(validateExcelFile()){
	
		$("#submitBlock").remove();
		$("#submitInput").hide();
		$("#reloadPage").css('display','block');
		setTimeout(function(){ $("#merchantUpload").submit(); }, 500);
	}
	});

		$("#reloadPage").click(function(){
	window.location.reload();
	});
	function validateExcelFile(){
	 var result = true;
	 $(".validationAlert").text("");
    
	 if($.trim($("#file").val()) == ""){
		 $("#file_alert").text("Please upload xlsx file");
		 result = false;
	 }
    
	 if($.trim($("#file").val()) != ""){
		 var ext = $('#file').val().split('.').pop().toLowerCase();

		 if(ext != 'xlsx'){
		    $("#file_alert").text("Please upload valid xlsx file");
		    result = false;
		 }else  if($("#file")[0].files[0].size > 10485760 ){
			    $("#file_alert").text("max file size is 10mb");
			    result = false;
	     }
		 
		
	 }

	 return result;

}

});
$.validator.addMethod("FileValidation", function (value, element)
		{
			return this.optional(element) || !(/\.(xlsx|xls|xlsm)$/i).test(file);
		});
		
		$("#sbi-emi-upload").validate(
		{
			onClick : true,

			errorElement: 'div',
			errorClass: 'help-block error-border',
			
			highlight: function (element, errorClass, validClass)
			{

				$(element).closest('.form-group').addClass("has-error");
				$(element).closest('.form-control').addClass("error-border");

			},
			unhighlight: function (element, errorClass, validClass) 
			{
				$(element).closest('.form-group').removeClass("has-error");
				$(element).closest('.form-control').removeClass("error-border");

			},
			rules :
			{
				file : 
				{
				required : true,
				FileValidation :true,
				}
			},
			messages:
			{
				file : 
				{			
					FileValidation: "Please Upload Valid excel file",
					required:"Please Select Valid Excel File"
				}
			}


			
});
