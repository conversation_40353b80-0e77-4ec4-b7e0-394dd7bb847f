$(document).ready(function () {



  // Datepicker Implementation
  var date = new Date(),
    yr = date.getFullYear(),
    month = date.getMonth() + 1,
    day = date.getDate(),
    todayDate = day + "-" + month + "-" + yr;

  var maxDate;
  var minDate;

  //activation request date
  $("#fromdate")
    .datepicker({
      format: "dd-mm-yyyy",
      autoclose: true,
    })
    .click("changeDate", function (selected) {
      minDate = moment(todayDate, "DD-MM-YYYY")
        .subtract(20, "years")
        .format("DD-MM-YYYY");
      maxDate = todayDate;
      $("#fromdate").datepicker("setStartDate", minDate);
      //$('#fromdate').datepicker('setEndDate', maxDate);
    });

  //to date
  $("#todate")
    .datepicker({
      format: "dd-mm-yyyy",
      autoclose: true,
    })
    .click("changeDate", function (selected) {
      var maxDate = $("#fromdate").val();
      var minDate = $("#fromdate").val();
      $("#todate").datepicker("setStartDate", minDate);
     // $("#todate").datepicker("setEndDate", maxDate);
    });

  if (
    $.trim($("#fromdate").val()) == "" ||
    $.trim($("#fromdate").val()) == "undefined"
  ) {
    $("#fromdate").datepicker("setDate", todayDate);
  }

  if (
    $.trim($("#todate").val()) == "" ||
    $.trim($("#todate").val()) == "undefined"
  ) {
    $("#todate").datepicker("setDate", todayDate);
  }
  // End Datepicker Implementation

  $("#downloadBin").click(function (event) {
    //stop submit the form, we will post it manually.
    event.preventDefault();

    // Get form
    var form = $("#downloadBinCategory")[0];

    // Create an FormData object
    var data = new FormData(form);

    if (validateBinDownloadForm()) {
      $("#downloadBinCategory").submit();
    }
  });



});

$("#table_id").hide();

window.setTimeout(function () {
  $("#card-alerts").hide();
}, 2000);




var myContextPath = $("#app_context_path").attr("content");
var token = $("#_csrf").attr("content");
var header = $("#_csrf_header").attr("content");
var postdata = {};

$("#searchBin").on("click", function (event) {
	
	if (!validateBinDownloadForm()) {
   
      
      return false
    }

	
	
  $("#table_id").show();

  // to fetch the acquirer reporting list
  var table = $("#table_id").DataTable({
    columns: $("#table_id")
      .find("thead tr th")
      .map(function () {
        return $(this).data();
      }),
    processing: true,
    serverSide: true,
    scrollCollapse: true,
    paging: true,
    bDestroy: true,
    columnDefs: [
      { targets: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], orderable: false }, // Replace [0] with the index of the column you want to disable sorting for
    ],
    createdRow: function (row, data, index) {
      var info = table.page.info();
      $("td", row)
        .eq(0)
        .html(index + 1 + info.page * info.length);
    },
    dom:
      "<'row'<'col-sm-12 col-md-12'l>>" +
      "<'row'<'col-sm-12'tr>>" +
      "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
    ajax: {
      url: contexturi + "/binDetails/bin-category-list",
      contentType: "application/json",
      type: "POST",
      timeout: "60000",
      data: function (d) {
        postdata.dtRequest = d;
        postdata.startDate = $("#fromdate").val();
        postdata.endDate = $("#todate").val();
        postdata.code = $("#code").val();
        postdata.status = $("#status").val();

        return JSON.stringify(postdata);
      },
      beforeSend: function (request) {
        request.setRequestHeader(header, token);
      },
      error: function (xhr, error, code) {
        if (
          error === "parsererror" ||
          error === "timeout" ||
          error === "error"
        ) {
          window.location.href = myContextPath + "/login?invalid";
        }
      },
    },
    columns: [
      {
        data: "name",
      },

      {
        data: "name",
      },
		
	{ "data": 'code',
	            	"render": function (data, type, row, meta) {
		            	if (row.code == "") {
            		    	data = data;
            		    }else{	
            			    data = '<a href="#" class="showMappingList" data-id="'+row.id+'" title="View">'+row.code+'</a>';
            		    }
		            	return data;
		            }	
	            },
      /*{
        data: "code",
      },*/
      {
        data: "description",
      },

      {
        data: "status",
        render: function (data, type, row, meta) {
          if (data != "" || data != null) {
            if (row.status == 1) {
              data = "Active";
            } else {
              data = "Not Active";
            }
          }
          return data;
        },
      },

      {
        data: "status",
        render: function (data, type, row, meta) {
          if (row.status == 1) {
            data =
              '<div class="custom-control custom-switch"><div id="customSwitchData' +
              row.id +
              '" data-status=' +
              row.status +
              " data-id=" +
              row.id +
              '></div><input type="checkbox" class="custom-control-input customSwitch1 bluestatus " id="customStatus_' +
              row.id +
              '" data-toggle="modal" checked data-target="#userStatusModal" ><label class="custom-control-label" for="customStatus_' +
              row.id +
              '"></label></div>';
          } else {
            data =
              '<div class="custom-control  custom-switch"><div id="customSwitchData' +
              row.id +
              '"  data-status=' +
              row.status +
              " data-id=" +
              row.id +
              '></div><input type="checkbox" class="custom-control-input customSwitch1" id="customStatus_' +
              row.id +
              '" data-toggle="modal" data-target="#userStatusModal" ><label class="custom-control-label" for="customStatus_' +
              row.id +
              '"></label></div>';
          }

          return data;
        },
      },

      {
        data: "startDate",
      },
      {
        data: "endDate",
      },
      {
        data: "binType",
      },

      {
        data: "binLength",
      },

      {
        data: "recordCreated",
      },
      {
        data: "recordUpdated",
      },
    ],
    order: [[0, "desc"]],
  });
  
  
  $("#table_id tbody").off("click", ".customSwitch1");


  
  $("#table_id tbody").on("click", ".customSwitch1", function (e) {
	  

	   
	   
    var id = this.id.split("_")[1];
 
    var status = $("#customSwitchData" + id).attr("data-status");
 
 
    $.ajax({
      url: contexturi + "/binDetails/update-bin-category-status",
      headers: {
        "X-CSRF-TOKEN": $("#_csrf").attr("content"),
      },
      type: "POST",
      data: {
        id: id,
        status: status,
      },
      success: function (result) {
        if (result == true) {
			
          $("#card-alerts3").css("display", "block");
          $("#card-alerts3").text("Status updated successfully");
 
          setTimeout(function () {
            $("#card-alerts3").hide();
          }, 4000);
 
          $("#table_id").DataTable().ajax.reload();
        } else if (result == false) {
          $("#card-alerts4").css("display", "block");
 
          $("#card-alerts4").text("Status not updated");
 
          setTimeout(function () {
            $("#card-alerts3").hide();
          }, 4000);
 
          $("#table_id").DataTable().ajax.reload();
        }
      },
    });
  });
  
  
    
	    $(document).on('click','.showMappingList',function(){	
			
			var  id = $(this).data('id');
		
            $form = $("<form action='"+myContextPath+"/binDetails/bin-category-mapping-list-view' method='get'></form>");
            $form.append("<input type='_csrf' name='_csrf' value='" + $("#_csrf").attr('content') + "'>");
            $form.append("<input type='hidden' name = 'id' value='" + id + "'>");
         
            
            $('body').append($form);
            $form.submit();
	     
	      });
  
});


  function validateBinDownloadForm() {
    var response = true;

    $(".validationAlert").text("");
    
    var alphanumericRegex = /^[a-zA-Z0-9]+$/;


    var start = $("#fromdate").datepicker("getDate");
    var end = $("#todate").datepicker("getDate");

    days = (end - start) / (1000 * 60 * 60 * 24);

    if ($.trim($("#fromdate").val()) == "") {
      $("#fromdate_error").text("Please select from date");
      response = false;
    } /*else if (Math.round(days) >= 31) {
      $("#30days_error").text("Please select dates between 31 days");
      response = false;
    }*/

    if ($.trim($("#todate").val()) == "") {
      $("#todate_error").text("Please select to date");
      response = false;
    } else if (start > end) {
      $("#exceeddays_error").text("please select todate more than fromDate ");
      response = false;
    }
    
  if($("#code").val() != ""){
		if(!alphanumericRegex.test($("#code").val())){
			$("#code_error").text("Please enter valid Code");
			response = false;
		}
	}
							
    return response;
  }


