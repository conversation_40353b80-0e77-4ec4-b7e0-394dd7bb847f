$(document).ready(function () {
	
	
	
	
	
	
					// DataTable
					var token = $('#_csrf').attr('content');
					var header = $('#_csrf_header').attr('content');
					// getting contextPath
					var myContextPath = $("#app_context_path").attr('content');

					// calling datatable request function
					var postdata = {};

					// Datepicker Implementation
					var date = new Date(), yr = date.getFullYear(), month = date
							.getMonth() + 1, day = date.getDate(), todayDate = day
							+ '-' + month + '-' + yr;

					$("#fromdate").val(todayDate);
					$("#todate").val(todayDate);
					
				$("#fromdate1").val($("#fromdate").val());
						
					$("#todate1").val($("#todate").val());	


					var date_input = $('input[name="date"]'); // our date
					// input has the
					// name
					// "date"
					var container = $('.bootstrap-iso form').length > 0 ? $(
							'.bootstrap-iso form').parent() : "body";
					date_input.datepicker({
						format : 'dd-mm-yyyy',
						container : container,
						todayHighlight : false,
						autoclose : true,
					})

					var maxDate;
					var minDate;
					$("#fromdate").datepicker({
						format : 'dd-mm-yyyy',
						autoclose : true,
					}).click(
							'changeDate',
							function(selected) {

								// moment.js used to convert and date value from
								// toDate
								minDate = moment(todayDate, 'DD-MM-YYYY')
										.subtract(20, 'years').format(
												'DD-MM-YYYY');
								maxDate = todayDate;
								$('#fromdate').datepicker('setStartDate',
										minDate);
								$('#fromdate')
										.datepicker('setEndDate', maxDate);

							});

					$("#todate").datepicker({
						  
						format : 'dd-mm-yyyy',
						autoclose : true,
						
					}).click('changeDate', function(selected) {
						var maxDate = todayDate;
						var minDate = $("#fromdate").val();
						$('#todate').datepicker('setStartDate', minDate);
						$('#todate').datepicker('setEndDate', maxDate);
							
			
					});
					
					
						$("#todate").datepicker({
						  
						format : 'dd-mm-yyyy',
						autoclose : true,
						
					}).on('changeDate', function(selected) {
						
					$("#todate1").val($("#todate").val());
					 //getFileName()
							
			
					});
					
						$("#fromdate").datepicker({
						  
						format : 'dd-mm-yyyy',
						autoclose : true,
						
					}).on('changeDate', function(selected) {
						
					$("#fromdate1").val($("#fromdate").val());
					 //getFileName()
							
			
					});
					// End Datepicker Implementation


	
	
	$("#reloadPage").hide();
	// THIS BLOCK IS RESPONSIBLE FOR DISPLAYING THE FILE NAME AFTER PICKING THE
	// FILE.
	$('.custom-file-input').on('change', function () {
		let fileName = $(this).val().split('\\').pop();
		if(fileName!=''){
			if(fileName!=''){
				$(this).next('.custom-file-label').addClass("selected").html(fileName);
			}
//			$(this).next('.custom-file-label').addClass("selected").html(fileName);
		}
//		$(this).next('.custom-file-label').addClass("selected").html(fileName);
	});

	// REMOVING ALERT AFTER DELAY OF 4 SECONDS.
	setTimeout(function () {
		$(".alert").alert('close');
	}, 4000);
//});

//  
$("#submit").click(function(e){
e.preventDefault();
if(validateExcelFile()){
	
	$("#submitBlock").remove();
	$("#submitInput").hide();
	$("#reloadPage").css('display','block');
	
	setTimeout(function(){ $("#emi-upload").submit(); }, 500);
	
}
});

$("#reloadPage").click(function(){
window.location.reload();
});
});


	


function validateExcelFile(){
	 var result = true;
	 $(".validationAlert").text("");
     
	 if($.trim($("#file").val()) == ""){
		 $("#file_alert").text("Please upload xlsx file");
		 result = false;
	 }
     
	 if($.trim($("#file").val()) != ""){
		 var ext = $('#file').val().split('.').pop().toLowerCase();

		 if(ext != 'xlsx'){
		    $("#file_alert").text("Please upload valid xlsx file");
		    result = false;
		    return result;
		 }
		 
		 if($("#file")[0].files[0].size > 10485760 ){
			    $("#file_alert").text("max file size is 10mb");
			    result = false;
			    return result;
	     }
	 }

	 return result;

}
	

		