$(document)
	.ready(
		function() {
			
			
		
			
			
			//hide data-table while page is rendering for the first time 	
			$("#template-table-div").hide();

			// DataTable
			var token = $('#_csrf').attr('content');
			var header = $('#_csrf_header').attr('content');
			// getting contextPath
			var myContextPath = $("#app_context_path").attr('content');

			// calling datatable request function
			var postdata = {};

			// to fetch the Acquirer list
			
			

						if (validateUserForm()) {
							$("#template-table-div").show();
							$('#template-table-id').dataTable().fnFilter();
						}
			var table = $('#template-table-id')
				.DataTable(
					{
						"processing": true,
						"serverSide": true,
						"scrollCollapse": true,
						"paging": true,
						 "bDestroy": true,
						
						"createdRow": function(row, data,
							index) {
							var info = table.page.info();
							$('td', row).eq(0).html(
								index + 1 + info.page
								* info.length);
						},
						
						
						"dom": "<'row'<'col-sm-12 col-md-12'l>>"
							+ "<'row'<'col-sm-12'tr>>"
							+ "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
						"ajax": {
							"url": myContextPath
								+ "/sms-email-template/list-template",
							"contentType": "application/json",
							"type": "POST",
							"timeout": "60000",
							"data": function(d) {
								postdata.dtRequest = d;
								postdata.acquirer = $('#acquirer').val();
								postdata.templateType = $('#templateType').val();
								postdata.txnType = $('#txnType').val();

								return JSON.stringify(postdata);

							},
							"beforeSend": function(request) {
								request.setRequestHeader(
									header, token);
							},
							"error": function(xhr, error, code) {

								if (error === 'parsererror'
									|| error === 'timeout'
									|| error === 'error') {
									window.location.href = myContextPath
										+ "/login?invalid";
								}
							}
						},
						"columns": [
							{
								"data": 'id',
								orderable: false,
							},

							{
								"data": 'templateName',
									orderable: false,
							},

							{
								"data": 'templateType',
								orderable: false,
								"render": function(data,
									type, row, meta) {

									if (data != ""
										|| data != null) {
										if (row.templateType == 1) {
											data = "E-Mail";
										} else {
											data = "SMS";
										}
									}
									return data;
								}
							},

							{
								"data": 'txnType',
									orderable: false,
								"render": function(data,
									type, row, meta) {

									if (data != ""
										|| data != null) {
										if (row.txnType == null) {
											data="NA";
										} 
									}
									return data;
								}
							},
							{
								"data": "status",
									orderable: false,
								"render": function(data,
									type, row, meta) {

									if (data != ""
										|| data != null) {
										if (row.status == 0) {
											data = "De-Active";
										} else {
											data = "Active";
										}
									}
									return data;
								}
							},

							{
								"data": "id",
								"searchable": false,
								"orderable": false,
								"render": function(data,
									type, row) {
									return '<button type="button" title="Edit" class="btn btn-primary btn-xs mx-auto d-block" data-toggle="modal" data-target="#modal-default" id="update"><i class="fa fa-edit"></i></button>';
								}
							},
							
										{
								"data": "id", // "data":
									orderable: false,
								// "transactionId"
								"render": function(data,
									type, row, meta) {
								

										return '<button type="button" class="btn btn-xs btn-primary showTransactionList" id="view" data-tid="'
											+ row.tid
											+ '" title="View"  ><i class="fa fa-eye " ></i></button>';
									} 
							

							},
						],
						"order": [[0, "desc"]]
					});

			// to EDIT or UPDATE SBI Details
			$("#template-table-id tbody").on("click", "#update", function() {
			
		
				var data = table.row($(this).parents("tr")).data();

				var id = data['id'];
				
				let $form = $("<form action='" + contexturi + "/sms-email-template/edit-template-view' method='post'></form>");
				$form.append("<input type='hidden' name='_csrf' value='" + $("#_csrf").attr('content') + "'>");
				$form.append("<input type='hidden' name = 'id' value='" + id + "'>");
				
				$('body').append($form);
				$form.submit();

			});

	$("#template-table-id tbody").on("click", "#view", function() {
				var data = table.row($(this).parents("tr")).data();

					var id = data['id'];
				
				let $form = $("<form action='" + contexturi + "/sms-email-template/view-template' method='post'></form>");
				$form.append("<input type='hidden' name='_csrf' value='" + $("#_csrf").attr('content') + "'>");
				$form.append("<input type='hidden' name = 'id' value='" + id + "'>");
				
				$('body').append($form);
				$form.submit();

			});

		
			
	
			// on search reload the form
			$("#search")
				.on(
					"click",
					function(event) {
						if (validateUserForm()) {
							$("#template-table-div").show();
							$('#template-table-id').dataTable().fnFilter();
						}
					});

			// calling special character function
			$(".no-special-char").keypress(function(event) {
				var character = String.fromCharCode(event.keyCode);
				return isValid(character);
			});

			setTimeout(function() {
				$(".alert").alert('close');
			}, 5000);

			$('.select2').select2();

				$("#template-table-id").on(
					"click",
					"[data-id='pass']",
					function() {
						var data = table.row($(this).parents("tr"))
							.data();

					});


			function validateUserForm() {

				var response = true;

				$(".validationAlert").text("");

				/*if ($.trim($("#acquirer").val()) == "0") {
					$("#acquirer_error").text("Please select aquirer");
					response = false;
				}*/
				if ($.trim($("#templateType").val()) == "-2") {
					$("#templateType_error").text("Please select template type");
					response = false;
				}
				return response;
			}



		});

