function getIntegrationData(id) {
	$('#midModal').modal('show');

	$("#merchantCode1").html($("#merchantCode_" + id).text().trim());
	$("#retailerRegion").html($("#retailorRegion_" + id).text().trim());
	$("#retailerGroup").html($("#retailerGroup_" + id).val().trim());
	$("#cityCode").html($("#cityCode_" + id).text().trim());
	$("#stateCode").html($("#stateCode_" + id).text().trim());
	$("#countryCode").html($("#countryCode_" + id).text().trim());
	$("#mappingAcqTgId").html($("#mappingAcqTgId_" + id).val().trim());
	$("#midRef1").html($("#midRef1_" + id).val());
	$("#midRef2").html($("#midRef2_" + id).val());
	$("#midRef3").html($("#midRef3_" + id).val());
	$("#midRef4").html($("#midRef4_" + id).val());
	$("#midRef5").html($("#midRef5_" + id).val());
	$("#midRef6").html($("#midRef6_" + id).val());
	$("#midRef7").html($("#midRef7_" + id).val());
	$("#proMid").html($("#proMid_" + id).val());


	var allowedSmsPay = $.trim($("#allowedSmsPay_" + id).val());

	if (allowedSmsPay == 0) {

		$("#allowedInsmsPay").html("No")

	}

	else {
		$("#allowedInsmsPay").html("Yes")

	}

	var cashbackFlag = $.trim($("#cashbackFlag_" + id).val());

	if (cashbackFlag == 0) {

		$("#cashBackFlag").html("No")

	}

	else {
		$("#cashBackFlag").html("Yes")

	}


	var isEmiAllow = $.trim($("#isemiAllow_" + id).val());

	if (isEmiAllow == 0) {

		$("#isEmiAllowed").html("Default(Not allowed)")

	}

	else if (isEmiAllow == 1) {
		$("#isEmiAllowed").html("Only CC Allowed")
	}
	else if (isEmiAllow == 2) {
		$("#isEmiAllowed").html("Only DC Allowed")
	}

	else if (isEmiAllow == 3) {
		$("#isEmiAllowed").html("Both CC and DC Allowed")
	}

	var ccEmiMode = $.trim($("#ccemiMode_" + id).val());


	if (ccEmiMode == 0) {

		$("#ccEmiMode").html("Default(Not allowed))")

	}

	else if (ccEmiMode == 1) {
		$("#ccEmiMode").html("File Based - onus and offus")
	}
	else if (ccEmiMode == 2) {
		$("#ccEmiMode").html("Onus tenure based and Offus file based")
	}


	var ccOffusEmi = $.trim($("#ccOffusEmi_" + id).val());


	if (ccOffusEmi == 0) {

		$("#ccOffusEmi").html("Default(Not allowed)")

	}

	else if (ccOffusEmi == 1) {
		$("#ccOffusEmi").html("Base TID")
	}
	else if (ccOffusEmi == 2) {
		$("#ccOffusEmi").html("EMI TID")
	}
	else if (ccOffusEmi == 3) {
		$("#ccOffusEmi").html("Offus EMI TID")
	}

	var ccOnusEmi = $.trim($("#ccOnusEmi_" + id).val());

	if (ccOnusEmi == 0) {

		$("#ccOnusEmi").html("Default(Not allowed)")

	}

	else if (ccOnusEmi == 1) {
		$("#ccOnusEmi").html("Base TID")
	}
	else if (ccOnusEmi == 2) {
		$("#ccOnusEmi").html("EMI TID")
	}
	else if (ccOnusEmi == 3) {
		$("#ccOnusEmi").html("Onus EMI TID")
	}
	else if (ccOnusEmi == 4) {
		$("#ccOnusEmi").html("Onus Tenure TID")
	}


	var matactiveFlag = $.trim($("#matactiveFlag_" + id).val());


	if (matactiveFlag == 0) {

		$("#matActiveFlag").html("Not Active")

	}

	else if (matactiveFlag == 1) {
		$("#matActiveFlag").html("Active (Primary)")
	}
	else if (matactiveFlag == 2) {
		$("#matActiveFlag").html("Active (Secondary)")
	}



	var mcashpostingFlag = $.trim($("#mcashpostingFlag_" + id).val());


	if (mcashpostingFlag == 0) {

		$("#mcashPostingFlag").html("No")

	}

	else if (mcashpostingFlag == 1) {
		$("#mcashPostingFlag").html("Yes")
	}

	var terminaldetailFlag = $.trim($("#terminaldetailFlag_" + id).val());

	if (terminaldetailFlag == 0) {

		$("#terminalDetailFlag").html("Terminal details required ")

	}

	else if (terminaldetailFlag == 1) {
		$("#terminalDetailFlag").html("Terminal details not required (default) ")
	}

	var instatdiscountFlag = $.trim($("#instatdiscountFlag_" + id).val());

	if (instatdiscountFlag == 0) {

		$("#instantDiscountFlag").html("No")

	}

	else if (instatdiscountFlag == 1) {
		$("#instantDiscountFlag").html("Yes")
	}

	var dccroutingFlag = $.trim($("#dccroutingFlag_" + id).val());


	if (dccroutingFlag == 0) {

		$("#dccRouting").html("Default (No Routing) ")

	}

	else if (dccroutingFlag == 1) {
		$("#dccRouting").html("Bin based Routing ")
	}

	else if (dccroutingFlag == 2) {
		$("#dccRouting").html("Currency code based Routing ")
	}


	var dcEmiTid = $.trim($("#dcEmiTid_" + id).val());

	if (dcEmiTid == 0) {

		$("#dcEmitId").html("Default(Not allowed)")

	}

	else if (dcEmiTid == 1) {
		$("#dcEmitId").html("Base TID")
	}
	else if (dcEmiTid == 2) {
		$("#dcEmitId").html("EMI TID")
	}

	else if (dcEmiTid == 3) {
		$("#dcEmitId").html("Aggregator TID")
	}

	else if (dcEmiTid == 4) {
		$("#dcEmitId").html("RULE BASED TID")
	}

}

$('#card-alerts').hide();
$('#dataTableBody').hide();

$(document).ready(function() {

	$('.select2').select2();

	let posIdVal = $('.posId').val();

	if (posIdVal == 0 && posIdVal) {
		$('#dataTableBody').hide();
		$(".alert").addClass("show");
		$('#card-alerts').show();
		$('#dataTableBody').hide();
		setTimeout(function() {
			$(".alert").alert('close');
		}, 3000);
	}
	else if (posIdVal && posIdVal != 0) {
		$('#dataTableBody').show();
	}

	$('#midModal').on('hidden.bs.modal', function() {
		location.reload();
	});

	$('#tidModal').on('hidden.bs.modal', function() {

		$('#dataTableBody').show();

		$('#myTab a[href="#menu3"]').tab('show')

		$("#tid-form1").submit();

	});



	$("#search").click(function(e) {

		if (validateUserForm()) {

			$("#acquirer-form").submit();
		}

	});



	$(".home").click(function(e) {
		$("#basic-form").submit();
	});

	$(".menu1").click(function(e) {
		$("#aggregator-form").submit();
	});

	$(".menu2").click(function(e) {
		$("#mid-form").submit();
	});


	$(".menu3").click(function(e) {
		$("#tid-form1").submit();
	});


	if (window.location.pathname == contexturi + '/merchantInfo/aggregatorInfo') {
		$('#dataTableBody').show();
		$('#myTab a[href="#menu1"]').tab('show')

	}

	if (window.location.pathname == contexturi + '/merchantInfo/midInfo') {
		$('#dataTableBody').show();
		$('#myTab a[href="#menu2"]').tab('show')

	}

	if (window.location.pathname == contexturi + '/merchantInfo/tidInfoUserName') {
		$('#dataTableBody').show();


		$('#myTab a[href="#menu3"]').tab('show')

		$(".getUserDetailsById").click(function() {

			var userid = $(this).data(
				'userid');

			$("#userName").val(userid);

			$("#tid-form").submit();
			//$('#tidModal').modal('show'); 

		});


	}


	if (window.location.pathname == contexturi + '/merchantInfo/tidInfo') {
		/*$('#dataTableBody').show();
		
	
$('#myTab a[href="#menu3"]').tab('show')*/

		/*	$(".getUserDetailsById").click(function(){
		
		var userid = $(this).data(
								'userid');
								
								
				 $("#userName").val(userid);
								
		
		$("#tid-form").submit();*/
		$('#tidModal').modal('show');




		/*	});*/


	}

	/*
		  var token = $('#_csrf').attr('content');
var header = $('#_csrf_header').attr('content');
	
var table = $('#table_id').DataTable({
	"processing": true,
	"serverSide": true,
	//"scrollY": $(document).height() - 400,
	"scrollCollapse": true,
	"paging": true,
	"bPaginate": false,
	"bFilter": false,
    
   
	initComplete: function() {
		 
		   $('.buttons-excel').html('<i class="far fa-arrow-alt-circle-down" /> Excel')
		 
		  },
	"ajax": {
		"headers": {

		},
		"url": contexturi + "/merchant/list-Of-Merchant-terminal-list",
		"contentType": "application/json",
		"type": "POST",
		"timeout":"60000",
		"data": function (d) {
	 
			
			var postdata ={};
			postdata.dtRequest = d;
			postdata.terminalMerchantId = $("#posid").val();
			postdata.transactionFrom = "07-08-2023";
			postdata.transactionTo = "07-08-2023";
			postdata.terminalAcquirer = -1$("#terminalAcquirer").val();
			postdata.userId = "";

			return JSON.stringify(postdata);
		},
		"beforeSend": function (request) {
			$("#search").attr('disabled',true);
			request.setRequestHeader(header, token);
		},
		"dataSrc" : function(json){
			  if(json.errors != null){	
				if(json.errors.merchantCreatedFrom !== undefined){
					$("#username_error").text(json.errors.userId);
				}
			  }
				return json.data;
			},
		"complete": function( settings, json ) {
			var newdata = JSON.parse(settings.responseText);
	    
			$("#search").attr('disabled',false);
			$(".getUserDetailsById").click(function(){
					$('#tidModal').modal('show'); 
				
		
			
			
			});
		},
		"error": function (xhr, error, code) {

			if (error === 'parsererror' || error === 'timeout') {
				window.location.href = contexturi + "/login?invalid";
			}
		}
	},
	"columns": [
		{
			"data": "userId",
			
			},
			
			{
			"data": "userId",
			"render": function (data, type, row, meta) {
				return '<a href="javascript:void(0);" class="getUserDetailsById" data-userid="'+data+'">'+data+'</a>';
				
				
					return '<button type="button" class="btn btn-xs btn-primary getUserDetailsById" data-userid="'
										+ data
										+ '" title="View"  ><i class="fa fa-eye " ></i></button>';
										
			 }
			 
			 
		},
		
	    

	],
    

}); 
	*/










	/*	$(".menu2").click(function() {
	
			$.ajax({
				url: contexturi + "/merchantInfo/midInfo",
				headers: {
					'X-CSRF-TOKEN': $('#_csrf').attr('content'),
				},
				type: "POST",
				data: {
					posId: $("#divisonId").val(),
			
				},
				success: function(result) {
					
		
	
					
	
				},
				
		
			});
		});*/

	function validateUserForm() {
			var numbers = /^[-+]?[0-9]+$/;

		var response = true;


		$(".validationAlert").text("");


		if ($.trim($("#deviceSerialNumber").val()) != "") {

			$("#posId").val(0)
		}



		if ($.trim($("#merchantCode").val()) != "") {
			$("#posId").val(0)
			if ($.trim($("#acquirer").val()) == "") {
				$("#acquirer_error").text("Please enter Card-Acquirer-TG");
				response = false;
			}



		}




		if ($.trim($("#tid").val()) != "") {
			$("#posId").val(0)
			if ($.trim($("#acquirer").val()) == "") {
				$("#acquirer_error").text("Please enter Card-Acquirer-TG");
				response = false;
			}



		}
		if ($.trim($("#tid").val()) != "") {
			$("#posId").val(0)
			if ($.trim($("#merchantCode").val()) == "") {
				$("#merchantCode_error").text("Please enter Merchant Code");
				response = false;
			}



		}


		if ($.trim($("#merchantCode").val()) == "" && $.trim($("#deviceSerialNumber").val()) == "") {

			if ($.trim($("#posId").val()) == "") {
				$("#posId_error").text("Please Enter Pos Id.");
				response = false;
			}else{
				if (!$("#posId").val().match(numbers)) {
			$("#posId_error")
				.text(
					"Only numbers are allowed");
			response = false;
		}

				
			}

		}


		


		return response;


	}

});










