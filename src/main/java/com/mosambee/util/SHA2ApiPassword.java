package com.mosambee.util;

import lombok.extern.log4j.Log4j2;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Log4j2
public final class SHA2ApiPassword {
    /**
     *   gets the SHA2 singleton object
     */
    public static SHA2ApiPassword getInstance () {
        if (singleton == null) {
            singleton = new SHA2ApiPassword();
        }
        return singleton;
    }

    /** Reference to the only SHA2 object */
    private static SHA2ApiPassword singleton = new SHA2ApiPassword();

    /**
     *   private constructor
     */
    private SHA2ApiPassword() {}

    /**
     *   this function converts a plain string to a hash string.
     *   The unicode string is converted to a byte array in
     *   big-endian order; the SHA2 is then executed.
     *   @param plain the original message
     *   @return hexadecimal number as string, 40 characters
     */
    public String hash ( String plain) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(plain.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();

            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}