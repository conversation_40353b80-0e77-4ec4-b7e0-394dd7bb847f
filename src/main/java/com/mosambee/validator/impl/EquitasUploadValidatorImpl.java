
package com.mosambee.validator.impl;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.mosambee.bean.EquitasUploadBean;
import com.mosambee.constants.BulkUploadMessages;
import com.mosambee.constants.RegexPatterns;
import com.mosambee.transformer.EquitasUploadTransformer;
import com.mosambee.validator.EquitasUploadValidator;

import lombok.extern.log4j.Log4j2;

/**
 * This class is responsible for validating all the fields coming in Upload bank
 * detail
 *
 * <AUTHOR>
 * @version 1.0
 *
 */
@Log4j2
@Component("equitasUploadValidator")
public class EquitasUploadValidatorImpl implements EquitasUploadValidator {
	@Autowired
	private EquitasUploadTransformer transformer;

	/**
	 * validateUploadBankDetailBean() is responsible for validating all fields of
	 * UploadBankDetailBean which are coming in terminal user upload excel file
	 *
	 */
	@Override
	public EquitasUploadBean validateEquitasUploadBean(EquitasUploadBean equitasUploadBean) {

		log.info("here i am");
		validatePosId(equitasUploadBean);
		validateMid(equitasUploadBean);
		validateTid(equitasUploadBean);
		validateUsername(equitasUploadBean);
		validateMatCode(equitasUploadBean);
		validateCashWithdrawal(equitasUploadBean);
		validateBalanceEnquiry(equitasUploadBean);
		validateName(equitasUploadBean);
		validateLocation(equitasUploadBean);
		validateTidType(equitasUploadBean);

		return equitasUploadBean;
	}

	private void validatePosId(EquitasUploadBean equitasUploadBean) {
		transformer.transformPosId(equitasUploadBean);
		// VALIDATE THE NUMBER_ONLY PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.NUMBER_ONLY.get());
		Matcher matcher = pattern.matcher(equitasUploadBean.getPosId());
		if (equitasUploadBean.getStatus() == null) {
			equitasUploadBean.setStatus("");
		}

		if (!matcher.matches()) {
			log.info("Pos ID  regex failed in validatePosId(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_POSID_ERROR.get(), equitasUploadBean.getPosId());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_POSID_ERROR.get());
		}
	}

	/**
	 * validateTid() is validating mid field .It is validating min length,max length
	 * and validating against{@RegexPattern ALPHA_NUMERIC_WITH_SPACE}
	 *
	 * @param uploadBankDetailBean
	 */
	private void validateMid(EquitasUploadBean equitasUploadBean) {
		transformer.transformMid(equitasUploadBean);
		// VALIDATE THE ALPHA_NUMERIC_WITH_SPACE PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.ALPHA_NUMERIC_WITHOUT_SPACE.get());
		Matcher matcher = pattern.matcher(equitasUploadBean.getMid());
		int mid = equitasUploadBean.getMid().length();

		if (!equitasUploadBean.getMid().isEmpty() && (!matcher.matches() || mid < 1 || mid > 100)) {

			log.info("MID regex failed in validateMid(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_UPLOAD_MID_REGEX_ERROR.get(), equitasUploadBean.getMid());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_UPLOAD_MID_REGEX_ERROR.get());

		}

		else if (equitasUploadBean.getMid().isEmpty() && !equitasUploadBean.getTid().isEmpty()) {
			log.info("MID regex failed in validateMid(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_UPLOAD_MID_TID_REGEX_ERROR.get(), equitasUploadBean.getMid());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_UPLOAD_MID_TID_REGEX_ERROR.get());
		}

	}

	/**
	 * validateTid() is validating mid field .It is validating min length,max length
	 * and validating against{@RegexPattern ALPHA_NUMERIC_WITH_SPACE}
	 *
	 * @param uploadBankDetailBean
	 */
	private void validateTid(EquitasUploadBean equitasUploadBean) {
		transformer.transformTid(equitasUploadBean);
		// VALIDATE THE ALPHA_NUMERIC_WITH_SPACE PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.ALPHA_NUMERIC_WITHOUT_SPACE.get());
		Matcher matcher = pattern.matcher(equitasUploadBean.getTid());
		int tid = equitasUploadBean.getTid().length();

		if (!equitasUploadBean.getTid().isEmpty() && (!matcher.matches() || tid < 1 || tid > 30)) {

			log.info("TID regex failed in validateTid(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_UPLOAD_TID_REGEX_ERROR.get(), equitasUploadBean.getTid());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_UPLOAD_TID_REGEX_ERROR.get());

		}
	}

	private void validateUsername(EquitasUploadBean equitasUploadBean) {
		transformer.transformUsername(equitasUploadBean);
		// VALIDATE THE NUMBER_ONLY PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.AEPS_NAME.get());
		Matcher matcher = pattern.matcher(equitasUploadBean.getUserName());
		// VALIDATE THE MIN AND MAX SIZE
		int userName = equitasUploadBean.getUserName().length();
		if (!matcher.matches() || userName < 1 || userName > 100) {
			log.info("Name regex failed in validateUsername(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_USERNAME_ERROR.get(), equitasUploadBean.getUserName());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_USERNAME_ERROR.get());
		}
	}

	/**
	 * validateCashWithdrawal() is validating Sale field .It is length validating
	 * against{@RegexPattern ALPHA_NUMERIC_WITH_SPACE} and it must be only 'Y' 'N'
	 *
	 * @param uploadBankDetailBean
	 */
	private void validateCashWithdrawal(EquitasUploadBean equitasUploadBean) {
		transformer.transformCashWithdrawal(equitasUploadBean);
		// VALIDATE THE ALPHA_NUMERIC_WITH_SPACE PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.CHARACTERS_ONLY.get());
		Matcher matcher = pattern.matcher(equitasUploadBean.getCashWithdrawal());
		// VALIDATE THE MIN AND MAX SIZE
		String cashWithdrawal = equitasUploadBean.getCashWithdrawal();

		if (isStringPresent(cashWithdrawal) && (!matcher.matches()
				|| (!cashWithdrawal.equalsIgnoreCase("Y") && !cashWithdrawal.equalsIgnoreCase("N")))) {
			log.info("cashWithdrawal regex failed in validateCashWithdrawal(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_CASH_WITHDRAWAL.get(), equitasUploadBean.getCashWithdrawal());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_CASH_WITHDRAWAL.get());
		}
	}

	/**
	 * validateBalanceEnquiry() is validating Sale field .It is length validating
	 * against{@RegexPattern ALPHA_NUMERIC_WITH_SPACE} and it must be only 'Y' 'N'
	 *
	 * @param uploadBankDetailBean
	 */
	private void validateBalanceEnquiry(EquitasUploadBean equitasUploadBean) {
		transformer.transformBalanceEnquiry(equitasUploadBean);
		// VALIDATE THE ALPHA_NUMERIC_WITH_SPACE PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.CHARACTERS_ONLY.get());
		Matcher matcher = pattern.matcher(equitasUploadBean.getBalanceEnquiry());
		// VALIDATE THE MIN AND MAX SIZE
		String balanceEnquiry = equitasUploadBean.getBalanceEnquiry();

		if (isStringPresent(balanceEnquiry) && (!matcher.matches()
				|| (!balanceEnquiry.equalsIgnoreCase("Y") && !balanceEnquiry.equalsIgnoreCase("N")))) {
			log.info("BalanceEnquiry regex failed in validateBalanceEnquiry(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_BALANCE_ENQUIRY.get(), equitasUploadBean.getBalanceEnquiry());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_BALANCE_ENQUIRY.get());
		}
	}

	/**
	 * validateMatCode(...) is responsible for validating the normalized
	 *
	 * Note:- We have not applied any kind of REGEX here for pattern matching.
	 *
	 * @param UploadBankDetailBean
	 *
	 */
	private void validateMatCode(EquitasUploadBean equitasUploadBean) {

		transformer.transformMatCode(equitasUploadBean);

		// VALIDATE THE MATCODE

		if (!equitasUploadBean.getMatCode().equalsIgnoreCase("EQUITASEQUITAS")
				&& !equitasUploadBean.getMatCode().equalsIgnoreCase("KOTAKOLIVE")) {

			log.error("MatCode validation failed : {}", equitasUploadBean.getMatCode());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_MATCODE_ERROR.get());
		}

	}

	/**
	 * validateClientId() is validating Client field .It is validating min
	 * length,max length and validating
	 * against{@RegexPattern ALPHA_NUMERIC_WITH_SPACE}
	 *
	 * @param microatmUploadBean
	 */
	public void validateName(EquitasUploadBean equitasUploadBean) {
		transformer.transformName(equitasUploadBean);
		// VALIDATE THE ALPHA_NUMERIC_WITH_SPACE PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.ALPHA_NUMERIC_WITH_SPACE.get());
		Matcher matcher = pattern.matcher(equitasUploadBean.getName());
		int name = equitasUploadBean.getName().length();
		if (!matcher.matches() || name < 1 || name > 50) {
			log.info("Name regex failed in validateClientId(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_UPLOAD_NAME_REGEX_ERROR.get(), equitasUploadBean.getName());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_UPLOAD_NAME_REGEX_ERROR.get());
		}
	}

	/**
	 * validateSecurityId() is validating Security Id field .It is validating min
	 * length,max length and validating
	 * against{@RegexPattern ALPHA_NUMERIC_WITH_SPACE}
	 *
	 * @param microatmUploadBean
	 */
	public void validateLocation(EquitasUploadBean equitasUploadBean) {
		transformer.transformLocation(equitasUploadBean);
		// VALIDATE THE ALPHA_NUMERIC_WITH_SPACE PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.ALPHA_NUMERIC_WITH_SPACE.get());
		Matcher matcher = pattern.matcher(equitasUploadBean.getLocation());
		int location = equitasUploadBean.getLocation().length();
		if (!matcher.matches() || location < 1 || location > 60) {
			log.info("Location  regex failed in validateLocation(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_UPLOAD_LOCATION_REGEX_ERROR.get(), equitasUploadBean.getLocation());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_UPLOAD_LOCATION_REGEX_ERROR.get());
		}

	}

	/**
	 * validateType() is validating type field .It is length validating
	 * against{@RegexPattern ALPHA_NUMERIC_WITH_SPACE} and it must be only 'Both'
	 * ,'Credit' and 'Debit'
	 *
	 * @param getEmiType
	 */
	private void validateTidType(EquitasUploadBean equitasUploadBean) {
		transformer.transformTidType(equitasUploadBean);
		// VALIDATE THE ALPHA_NUMERIC_WITH_SPACE PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.CHARACTERS_ONLY.get());
		Matcher matcher = pattern.matcher(equitasUploadBean.getTidType());
		// VALIDATE THE MIN AND MAX SIZE++++
		String tidType = equitasUploadBean.getTidType();
		if (!matcher.matches() || (!tidType.equalsIgnoreCase("BOTH") && !tidType.equalsIgnoreCase("CREDIT")
				&& !tidType.equalsIgnoreCase("DEBIT"))) {
			log.info("type  regex failed in validateType(): {}, for value: {}",
					BulkUploadMessages.EQUITAS_UPLOAD_TIDTYPE_REGEX_ERROR.get(), equitasUploadBean.getTidType());
			equitasUploadBean.appendStatus(BulkUploadMessages.EQUITAS_UPLOAD_TIDTYPE_REGEX_ERROR.get());
		}
	}

	private boolean isStringPresent(String str) {
		if (str == null) {
			return false;
		} else {
			if (str.equals("")) {
				return false;
			}
		}
		return true;
	}
}
