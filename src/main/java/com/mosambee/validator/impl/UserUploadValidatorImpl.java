package com.mosambee.validator.impl;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.mosambee.bean.UserUploadBean;
import com.mosambee.constants.BulkUploadMessages;
import com.mosambee.constants.RegexPatterns;
import com.mosambee.service.impl.UserBindingServiceImpl;
import com.mosambee.validator.UserUploadValidator;

import lombok.extern.log4j.Log4j2;

@Component
@Log4j2
public class UserUploadValidatorImpl implements UserUploadValidator {
	@Autowired
	UserBindingServiceImpl service;

	@Override
	public UserUploadBean validateUserBean(UserUploadBean bean, Map<Integer, String> acquirers,
			Map<Integer, String> deviceType, Long mdaId) {
		bean.setReason("");
		validateUserName(bean);
		validateSerialnumber(bean);
		validateAcquirerName(bean);
		validateDeviceType(bean);
		validateDeviceMappingStatus(bean);
		validateRemark(bean);
		validateAcknowledgementFlag(bean);
		if (bean.getReason().equals("")) {
			validateDeviceTypeAndAcquirerwithDB(bean, acquirers, deviceType, mdaId);
		}

		return bean;
	}

	private void validateDeviceTypeAndAcquirerwithDB(UserUploadBean bean, Map<Integer, String> acquirers,
			Map<Integer, String> deviceType, long mdaId) {

		String key = service.getKey(acquirers, bean.getAcquirerName());
		String deviceId = service.getKey(deviceType, bean.getDeviceType());
		log.info(key);
		log.info(deviceId);

		if (key != null && !key.isEmpty() && Integer.parseInt(key) > 0) {
			bean.setAcqId(Integer.parseInt(key));
			log.info("acqId{}", bean.getAcqId());

			log.info("userUploadBean1{}", bean);

		} else {

			bean.appendStatus("Acquirer not present");
			log.info("userUploadBean1{}", bean);

		}
		if (deviceId != null && !deviceId.isEmpty() && Integer.parseInt(deviceId) > 0) {

			bean.setDeviceId(Integer.parseInt(deviceId));
			log.info("deviceId--------{}", bean.getDeviceId());

		} else {

			bean.appendStatus("Device type not present");	

		}

		if (mdaId <= 0) {

			bean.appendStatus("User is not map to the device.");
		}

	}

	private void validateAcknowledgementFlag(UserUploadBean bean) {
		String ackFlag = bean.getAcknowledgementFlag();
		ackFlag = ackFlag.trim();
		if (ackFlag != null && !ackFlag.isEmpty() && !ackFlag.equalsIgnoreCase("0") && !ackFlag.equalsIgnoreCase("1")) {
			log.info("regex failed in validateAcknowledgementFlag(): {}, for value: {}",
					BulkUploadMessages.ACKNOWLEDGEMENTFLAG_STATUS_ERROR.get(), ackFlag);
			bean.appendStatus(BulkUploadMessages.ACKNOWLEDGEMENTFLAG_STATUS_ERROR.get());

		}

	}

	private void validateRemark(UserUploadBean bean) {
		String remark = bean.getRemark();
		remark = remark.trim();

		Pattern pattern = Pattern.compile(RegexPatterns.ALPHA_NUMERIC_WITH_SPACE.get());
		Matcher matcher = pattern.matcher(remark);
		int remarkLength = remark.length();
		if (bean.getRemark() != null && !bean.getRemark().isEmpty() && !matcher.matches() && remarkLength < 2
				&& remarkLength > 120) {
			log.info("Remark failed in validateRemark(): {}, for value: {}", BulkUploadMessages.REMARK_ERROR.get(),
					remark);
			bean.appendStatus(BulkUploadMessages.REMARK_ERROR.get());
		}

	}

	private void validateDeviceMappingStatus(UserUploadBean bean) {
		String devicemappingStatus = bean.getDeviceMappingStatus();
		devicemappingStatus = devicemappingStatus.trim();
		if (devicemappingStatus != null && !devicemappingStatus.isEmpty() && !devicemappingStatus.equalsIgnoreCase("0")
				&& !devicemappingStatus.equalsIgnoreCase("1")) {
			log.info("regex failed in validateDeviceMappingStatus(): {}, for value: {}",
					BulkUploadMessages.DEVICE_MAPPING_STATUS_ERROR.get(), bean.getDeviceMappingStatus());
			bean.appendStatus(BulkUploadMessages.DEVICE_MAPPING_STATUS_ERROR.get());

		}

	}

	private void validateDeviceType(UserUploadBean bean) {
		String deviceType = bean.getDeviceType();
		deviceType = deviceType.trim();
		Pattern pattern = Pattern.compile(RegexPatterns.ALPHA_NUMERIC_WITHOUT_SPACE_AND_UNDERSCORE.get());
		Matcher matcher = pattern.matcher(deviceType);
		int deviceTypeLength = deviceType.length();
		if (bean.getDeviceType().isEmpty() || bean.getDeviceType() == null || !matcher.matches() || deviceTypeLength < 2
				|| deviceTypeLength > 100) {
			log.info("Device Type failed in validateDeviceType(): {}, for value: {}",
					BulkUploadMessages.DEVICE_TYPE_ERROR.get(), bean.getDeviceType());
			bean.appendStatus(BulkUploadMessages.DEVICE_TYPE_ERROR.get());
		}

	}

	private void validateAcquirerName(UserUploadBean bean) {
		String acq = bean.getAcquirerName();
		acq = acq.trim();
		Pattern pattern = Pattern.compile(RegexPatterns.CHARACTERS_ONLY.get());
		Matcher matcher = pattern.matcher(acq);
		int acqLength = acq.length();
		if (bean.getAcquirerName() == null || acq.isEmpty() || !matcher.matches() || acqLength < 2 || acqLength > 20) {
			log.info("Acquirer name failed in validateAcquirer(): {}, for value: {}",
					BulkUploadMessages.ACQ_ERROR.get(), acq);
			bean.appendStatus(BulkUploadMessages.ACQ_ERROR.get());
		}

	}

	private void validateSerialnumber(UserUploadBean bean) {
		String serialNumber = bean.getSerialNumber();
		serialNumber = serialNumber.trim();

		Pattern pattern = Pattern.compile(RegexPatterns.ALPHA_NUMERIC_WITHOUT_SPACE.get());
		Matcher matcher = pattern.matcher(serialNumber);
		int serialNumberLength = serialNumber.length();
		if (bean.getSerialNumber().isEmpty() || bean.getSerialNumber() == null || !matcher.matches()
				|| serialNumberLength < 4 || serialNumberLength > 60) {
			log.info("Serial Number failed in validateSerialNumber(): {}, for value: {}",
					BulkUploadMessages.SERIAL_NUMBER_ERROR.get(), serialNumber);
			bean.appendStatus(BulkUploadMessages.SERIAL_NUMBER_ERROR.get());
		}

	}

	private void validateUserName(UserUploadBean bean) {
		String username = bean.getUserName();
		username = username.trim();

		// VALIDATE THE ALPHA_NUMERIC_WITHOUT_SPACE PATTERN
		Pattern pattern = Pattern.compile(RegexPatterns.TERMINAL_USER_NO.get());
		Matcher matcher = pattern.matcher(username);
		int usernameLength = username.length();   
		if (bean.getUserName() == null || username.isEmpty() || !matcher.matches() || usernameLength < 8
				|| usernameLength > 100) {
			log.info("Username regex failed in validateUsername(): {}, for value: {}",
					BulkUploadMessages.USERNAME_ERROR.get(), username);
			bean.appendStatus(BulkUploadMessages.USERNAME_ERROR.get());
		}

	}

	
}
