package com.mosambee.validator.impl;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;
import com.mosambee.bean.CreateAPIGroup;
import com.mosambee.bean.MIDBulkUpload;
import com.mosambee.constants.APIPasswordConfigConstants;
import com.mosambee.constants.CommonConstants;
import com.mosambee.constants.RegexPatterns;
import com.mosambee.validator.APIPasswordConfigValidator;

/**
 * This class is used to validate all forms of 'API Password Config' module.
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component("apiPasswordConfigValidator")
public class APIPasswordConfigValidatorImpl implements APIPasswordConfigValidator {

	private static final Logger log = LogManager.getLogger(APIPasswordConfigValidatorImpl.class);

	/**
	 * This method is basically used to call validate methods on fields of
	 * CreateAPIGroup bean. This method is responsible to validate and set
	 * validation status for bean CreateAPIGroup bean.
	 * 
	 * @param CreateAPIGroup : The bean which you want to validate.
	 * <AUTHOR>
	 */
	@Override
	public void validateCreateAPIGroupData(CreateAPIGroup createAPIGroup) {
		createAPIGroup.setValidate(true);
		createAPIGroup.setValidationMsg(CommonConstants.EMPTY_STRING.get());
		validateAPIURL1(createAPIGroup);
		validateAPIURL2(createAPIGroup);
		validateDivisionReference1(createAPIGroup);
		validateDivisionReference2(createAPIGroup);
		validateDivisionReference3(createAPIGroup);
		validateDivisionReference4(createAPIGroup);

		log.info("validation status : {}", createAPIGroup.isValidate());
	}

	/**
	 * This method is basically used to call validate methods on fields of
	 * {@link MIDBulkUpload} bean. This method is responsible to validate and set
	 * validation status for {@link MIDBulkUpload} bean.
	 * 
	 * @param {@link MIDBulkUpload} : The bean which you want to validate.
	 * <AUTHOR>
	 */
	@Override
	public void validateMIDBulkUpload(MIDBulkUpload midBulkUpload) {
		midBulkUpload.setValidate(true);
		midBulkUpload.setStatus(CommonConstants.EMPTY_STRING.get());
		validateDivisionCode(midBulkUpload);
		validateMID(midBulkUpload);
		validateTID(midBulkUpload);
	}

	/**
	 * This method is use to validate "DivisionCode" field of {@link MIDBulkUpload}
	 * object This method checks that whether field is null or not. Length should be
	 * less than or equal to 45. Field value should be Alphanumeric with space
	 * without trailing or leading space.
	 * 
	 * @param midBulkUpload : Is the object of {@MIDBulkUpload}
	 */
	void validateDivisionCode(MIDBulkUpload midBulkUpload) {
		String divisionCode = midBulkUpload.getDivisionCode();
		divisionCode = divisionCode.trim();
		midBulkUpload.setDivisionCode(divisionCode);

		if (!isStringPresent(divisionCode)) {
			midBulkUpload.appendStatus(APIPasswordConfigConstants.DIVISION_CODE.get());
			midBulkUpload.appendStatus(APIPasswordConfigConstants.IS_REQUIRED.get());
			midBulkUpload.setValidate(false);
		} else {
			if (divisionCode.length() <= 45 && divisionCode.length() >= 3) {
				if (!divisionCode
						.matches(RegexPatterns.ALPHA_NUMERIC_WITH_SPACE_WITHOUT_LEADING_AND_TRAILING_SPACE.get())) {
					midBulkUpload.appendStatus(APIPasswordConfigConstants.DIVISION_CODE.get());
					midBulkUpload.appendStatus(APIPasswordConfigConstants.SHOULD_BE_ALPHA_NUMERIC_WITH_SPACE.get());
					midBulkUpload.setValidate(false);
				}
			} else {
				midBulkUpload.appendStatus(APIPasswordConfigConstants.DIVISION_CODE.get());
				midBulkUpload.appendStatus(APIPasswordConfigConstants.DIVISION_CODE_MAX_LENGTH.get());
				midBulkUpload.setValidate(false);
			}
		}

	}

	/**
	 * This method is use to validate "MID" field of {@link MIDBulkUpload} object *
	 * This method checks that whether field is null or not. Length should be less
	 * than or equal to 45. Field value should be Alphanumeric with space without
	 * trailing or leading space.
	 * 
	 * @param midBulkUpload : Is the object of {@MIDBulkUpload}
	 */
	void validateMID(MIDBulkUpload midBulkUpload) {
		String mid = midBulkUpload.getMid();
		mid = mid.trim();
		midBulkUpload.setMid(mid);

		if (!isStringPresent(mid)) {
			midBulkUpload.appendStatus(APIPasswordConfigConstants.MID.get());
			midBulkUpload.appendStatus(APIPasswordConfigConstants.IS_REQUIRED.get());
			midBulkUpload.setValidate(false);
		} else {
			if (mid.length() <= 45 && mid.length() >= 3) {
				if (!mid.matches(RegexPatterns.ALPHA_NUMERIC_WITH_SPACE_WITHOUT_LEADING_AND_TRAILING_SPACE.get())) {
					midBulkUpload.appendStatus(APIPasswordConfigConstants.MID.get());
					midBulkUpload.appendStatus(APIPasswordConfigConstants.SHOULD_BE_ALPHA_NUMERIC_WITH_SPACE.get());
					midBulkUpload.setValidate(false);
				}
			} else {
				midBulkUpload.appendStatus(APIPasswordConfigConstants.MID.get());
				midBulkUpload.appendStatus(APIPasswordConfigConstants.MID_MAX_LENGTH.get());
				midBulkUpload.setValidate(false);
			}
		}
	}

	/**
	 * This method is use to validate "TID" field of {@link MIDBulkUpload} object
	 * This method checks that whether field is null or not. Length should be less
	 * than or equal to 30 and greater than equal to 8. Field value should be
	 * Alphanumeric with space without trailing or leading space.
	 * 
	 * @param midBulkUpload : Is the object of {@MIDBulkUpload}
	 */
	void validateTID(MIDBulkUpload midBulkUpload) {
		String tid = midBulkUpload.getTid();
		tid = tid.trim();
		midBulkUpload.setTid(tid);

		if (!isStringPresent(tid)) {
			midBulkUpload.appendStatus(APIPasswordConfigConstants.TID.get());
			midBulkUpload.appendStatus(APIPasswordConfigConstants.IS_REQUIRED.get());
			midBulkUpload.setValidate(false);
		} else {
			if (tid.length() <= 30 && tid.length() >= 8) {
				if (!tid.matches(RegexPatterns.ALPHA_NUMERIC_WITH_SPACE_WITHOUT_LEADING_AND_TRAILING_SPACE.get())) {
					midBulkUpload.appendStatus(APIPasswordConfigConstants.TID.get());
					midBulkUpload.appendStatus(APIPasswordConfigConstants.SHOULD_BE_ALPHA_NUMERIC_WITH_SPACE.get());
					midBulkUpload.setValidate(false);
				}
			} else {
				midBulkUpload.appendStatus(APIPasswordConfigConstants.TID.get());
				midBulkUpload.appendStatus(APIPasswordConfigConstants.TID_MIN_MAX_LENGTH.get());
				midBulkUpload.setValidate(false);
			}
		}
	}

	/**
	 * This method is use to validate "API URL 1" field of {@link CreateAPIGroup}
	 * object This method checks that whether field is null or not. Length should be
	 * less than or equal to 250. Field value should be URL.
	 * 
	 * @param createAPIGroup : Is the object of {@link CreateAPIGroup}
	 */
	void validateAPIURL1(CreateAPIGroup createAPIGroup) {
		String apiURL1 = createAPIGroup.getApiURL1();
		apiURL1 = apiURL1.trim();
		createAPIGroup.setApiURL1(apiURL1);

		if (isStringPresent(apiURL1)) {
			if (apiURL1.length() <= 250) {
				if (!apiURL1.matches(RegexPatterns.URL.get())) {
					createAPIGroup.appendStatus(APIPasswordConfigConstants.API_URL_1.get());
					createAPIGroup.appendStatus(APIPasswordConfigConstants.SHOULD_BE_URL.get());
					createAPIGroup.setValidate(false);
				}
			} else {
				createAPIGroup.appendStatus(APIPasswordConfigConstants.API_URL_1.get());
				createAPIGroup.appendStatus(APIPasswordConfigConstants.API_URL_MAX_LENGTH.get());
				createAPIGroup.setValidate(false);
			}
		}
	}

	/**
	 * This method is use to validate "API URL 2" field of {@link CreateAPIGroup}
	 * object This method checks that whether field is null or not. Length should be
	 * less than or equal to 250. Field value should be URL.
	 * 
	 * @param createAPIGroup : Is the object of {@link CreateAPIGroup}
	 */
	void validateAPIURL2(CreateAPIGroup createAPIGroup) {
		String apiURL2 = createAPIGroup.getApiURL2();
		apiURL2 = apiURL2.trim();
		createAPIGroup.setApiURL2(apiURL2);

		if (isStringPresent(apiURL2)) {
			if (apiURL2.length() <= 250) {
				if (!apiURL2.matches(RegexPatterns.EMAIL.get())) {
					createAPIGroup.appendStatus(APIPasswordConfigConstants.API_URL_2.get());
					createAPIGroup.appendStatus(APIPasswordConfigConstants.SHOULD_BE_URL.get());
					createAPIGroup.setValidate(false);
				} else {
					createAPIGroup.setValidate(true);
				}
			} else {
				createAPIGroup.appendStatus(APIPasswordConfigConstants.API_URL_2.get());
				createAPIGroup.appendStatus(APIPasswordConfigConstants.API_URL_MAX_LENGTH.get());
				createAPIGroup.setValidate(false);
			}
		}
	}

	/**
	 * This method is use to validate "DivisionReference1" field of
	 * {@link CreateAPIGroup} object This method checks that whether field is null
	 * or not. Length should be less than or equal to 45. Field value should be
	 * Alphanumeric with space without trailing or leading space.
	 * 
	 * @param createAPIGroup : Is the object of {@link CreateAPIGroup}
	 */
	void validateDivisionReference1(CreateAPIGroup createAPIGroup) {
		String divisionReference1 = createAPIGroup.getDivisionRef1();
		divisionReference1 = divisionReference1.trim();
		createAPIGroup.setDivisionRef1(divisionReference1);

		if (isStringPresent(divisionReference1)) {
			if (divisionReference1.length() <= 45) {

				createAPIGroup.appendStatus(APIPasswordConfigConstants.DIVISION_REFERENCE_1.get());
				createAPIGroup.appendStatus(APIPasswordConfigConstants.DIVISION_REFERENCE_MAX_LENGTH.get());
				createAPIGroup.setValidate(false);
			} else {
				createAPIGroup.setValidate(true);
			}
		}
		createAPIGroup.setValidate(true);
	}

	/**
	 * This method is use to validate "DivisionReference2" field of
	 * {@link CreateAPIGroup} object This method checks that whether field is null
	 * or not. Length should be less than or equal to 45. Field value should be
	 * Alphanumeric with space without trailing or leading space.
	 * 
	 * @param createAPIGroup : Is the object of {@link CreateAPIGroup}
	 */
	void validateDivisionReference2(CreateAPIGroup createAPIGroup) {
		String divisionReference2 = createAPIGroup.getDivisionRef2();
		divisionReference2 = divisionReference2.trim();
		createAPIGroup.setDivisionRef2(divisionReference2);

		if (isStringPresent(divisionReference2)) {
			if (divisionReference2.length() <= 45) {

				createAPIGroup.appendStatus(APIPasswordConfigConstants.DIVISION_REFERENCE_2.get());
				createAPIGroup.appendStatus(APIPasswordConfigConstants.DIVISION_REFERENCE_MAX_LENGTH.get());
				createAPIGroup.setValidate(false);
			} else {
				createAPIGroup.setValidate(true);
			}
		}
		createAPIGroup.setValidate(true);
	}

	/**
	 * This method is use to validate "DivisionReference3" field of
	 * {@link CreateAPIGroup} object This method checks that whether field is null
	 * or not. Length should be less than or equal to 45. Field value should be
	 * Alphanumeric with space without trailing or leading space.
	 * 
	 * @param createAPIGroup : Is the object of {@link CreateAPIGroup}
	 */
	void validateDivisionReference3(CreateAPIGroup createAPIGroup) {
		String divisionReference3 = createAPIGroup.getDivisionRef3();
		divisionReference3 = divisionReference3.trim();
		createAPIGroup.setDivisionRef3(divisionReference3);

		if (isStringPresent(divisionReference3)) {
			if (divisionReference3.length() <= 45) {
				createAPIGroup.appendStatus(APIPasswordConfigConstants.DIVISION_REFERENCE_3.get());
				createAPIGroup.appendStatus(APIPasswordConfigConstants.DIVISION_REFERENCE_MAX_LENGTH.get());
				createAPIGroup.setValidate(false);
			} else {
				createAPIGroup.setValidate(true);
			}
		}
		createAPIGroup.setValidate(true);
	}

	/**
	 * This method is use to validate "DivisionReference4" field of
	 * {@link CreateAPIGroup} object This method checks that whether field is null
	 * or not. Length should be less than or equal to 45. Field value should be
	 * Alphanumeric with space without trailing or leading space.
	 * 
	 * @param createAPIGroup : Is the object of {@link CreateAPIGroup}
	 */
	void validateDivisionReference4(CreateAPIGroup createAPIGroup) {
		String divisionReference4 = createAPIGroup.getDivisionRef4();
		divisionReference4 = divisionReference4.trim();
		createAPIGroup.setDivisionRef4(divisionReference4);

		if (isStringPresent(divisionReference4)) {
			if (divisionReference4.length() <= 45) {
				createAPIGroup.appendStatus(APIPasswordConfigConstants.DIVISION_REFERENCE_4.get());
				createAPIGroup.appendStatus(APIPasswordConfigConstants.DIVISION_REFERENCE_MAX_LENGTH.get());
				createAPIGroup.setValidate(false);
			} else {
				createAPIGroup.setValidate(true);
			}
		}
		createAPIGroup.setValidate(true);
	}

	/**
	 * This method is used check whether the string "str" is "null" or "". If
	 * parameter "str" is null or "" then it will return false otherwise return
	 * true. This method is useful to avoid "NullPointerException", or validate
	 * string is "" or not.
	 * 
	 * @param str : String to be checked.
	 * @return boolean : Return "True" if string present Return "False" is string is
	 *         not present or null.
	 * <AUTHOR>
	 */
	private boolean isStringPresent(String str) {
		if (str == null) {
			return false;
		} else {
			if (str.equals("")) {
				return false;
			}
		}
		return true;
	}

}
