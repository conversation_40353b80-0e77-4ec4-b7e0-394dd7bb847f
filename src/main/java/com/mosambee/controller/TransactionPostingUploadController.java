package com.mosambee.controller;

import com.mosambee.util.ExcelFileCheck;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import com.mosambee.bean.CustomUser;
import com.mosambee.bean.TxnPostingBean;
import com.mosambee.bean.TxnPostingDatatablesRequestBean;
import com.mosambee.bean.datatables.DataTablesResponse;
import com.mosambee.constants.CommonConstants;
import com.mosambee.constants.ViewLayer;
import com.mosambee.service.TransactionPostingUploadService;

import java.io.IOException;

/**
 * This class is responsible for transaction posting upload and list
 * 
 * <AUTHOR>
 *
 */
@Log4j2
@Controller
@RequestMapping("/txn-posting")
public class TransactionPostingUploadController {


	@Autowired
	TransactionPostingUploadService transactionPostingUploadService;

	/**
	 * API to view upload transaction posting details page
	 * 
	 * @return
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/upload")
	public String txnPostingUpload() {
		log.info("GET /txn-posting/upload");
		return ViewLayer.TXNPOSTING_UPLOAD.get();
	}

	/**
	 * getBulkUploadFormat() is responsible for downloading the txn posting bulk
	 * upload format
	 * 
	 * @return
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/upload-format")
	public Object getBulkUploadFormat() {

		log.info("GET /txn-posting/upload-format");

		Resource resource = transactionPostingUploadService.getBulkUploadFormat();

		if (null != resource) {
			log.info("Sending  txn posting bulk upload format in response.");
			return ResponseEntity.ok().contentType(MediaType.parseMediaType(CommonConstants.EXCEL_CONTENT_TYPE.get()))
					.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"txn-posting-upload-format.xlsx\"")
					.body(resource);
		} else {
			log.error("Error ocurred while downloading txn posting bulk upload sample file.");
			ModelAndView modelAndView = new ModelAndView(ViewLayer.TXNPOSTING_UPLOAD.get());
			modelAndView.addObject("txn_posting_bulk_upload_format_error", true);
			return modelAndView;
		}
	}

	/**
	 * @param file MultiPartFile that we will receive in the request
	 * @return Object
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/upload-txn")
	public Object bulkUpload(@RequestParam MultipartFile file) throws IOException {
		log.info("POST /txn-posting/upload-txn");

		log.info("Request time: {}", System.currentTimeMillis());
	
		String excelFileCheckFlag = ExcelFileCheck.scanExcelFile(file.getInputStream());
		log.info("Excel sheet serching for ASPOSE values in TransactionPostingUploadController ` :" + excelFileCheckFlag);
		if ("success".equalsIgnoreCase(excelFileCheckFlag) || excelFileCheckFlag.equalsIgnoreCase("0")) {

			CustomUser user = (CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			Long createdBy = Long.parseLong(user.getMyMap().get("id"));
			// Get the resource from service
			Resource resource = transactionPostingUploadService.processBulkUploadExcel(file, createdBy);
			if (null != resource) {
				log.info("Response time: {}", System.currentTimeMillis());
				return ResponseEntity.ok().contentType(MediaType.parseMediaType(CommonConstants.EXCEL_CONTENT_TYPE.get()))
						.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"TxnPostingUploadResponse.xlsx\"")
						.body(resource);
			} else {
				ModelAndView modelAndView = new ModelAndView(ViewLayer.TXNPOSTING_UPLOAD.get());
				modelAndView.addObject("invalid", true);
				return modelAndView;
			  }
		}
		else{
			log.info("Error ocurred while uploading TransactionPosting Upload file.During the excel file serching");
			ModelAndView modelAndView = new ModelAndView(ViewLayer.TXNPOSTING_UPLOAD.get());
			modelAndView.addObject("invalid", true);
			return modelAndView;
		}
	}

	/**
	 * Api to view txn posting list page
	 * 
	 * @return String
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_USER') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/list-view")
	public String viewList() {
		log.info("GET /list-view");
		return ViewLayer.TXNPOSTING_LIST.get();
	}

	/**
	 * Api to fetch datatables response for txn posting search list
	 * 
	 * @param dtRequest
	 * @return ResponseEntity<DataTablesResponse<TxnPostingBean>>
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_USER') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/list-view")
	public ResponseEntity<DataTablesResponse<TxnPostingBean>> viewSearchList(
			@RequestBody TxnPostingDatatablesRequestBean dtRequest) {
		log.info("POST /txn-posting/list-view {}", dtRequest);

		DataTablesResponse<TxnPostingBean> dtResponse = transactionPostingUploadService.getSearchList(dtRequest);
		log.info("POST /list-view {}", dtResponse);

		return new ResponseEntity<>(dtResponse, HttpStatus.OK);
	}
}