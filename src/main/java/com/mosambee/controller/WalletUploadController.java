package com.mosambee.controller;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.mosambee.constants.CommonConstants;
import com.mosambee.constants.ViewLayer;
import com.mosambee.service.WalletUploadService;
import com.mosambee.util.ExcelFileCheck;

import lombok.extern.log4j.Log4j2;

@Controller
@RequestMapping("/wallet-upload")
@Log4j2
public class WalletUploadController {
	@Autowired
	WalletUploadService service;

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/wallet-upload-view")
	public String walletUpload() {
		log.info("GET/wallet-upload/wallet-upload-view");
		return ViewLayer.WALLET_UPLOAD_VIEW.get();

	}

	/**
	 * Api to download wallet upload format excel
	 *
	 * @return Object
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/download-wallet-upload-format")
	public Object downloadWalletUploadFormat() {
		log.info("GET /wallet-upload/download-wallet-upload-format");
		Resource resource = service.getWalletUploadFormat();
		ModelAndView modelAndView = new ModelAndView(ViewLayer.WALLET_UPLOAD_VIEW.get());
		if (null != resource) {
			log.info("Sending  wallet upload format in response.");
			return ResponseEntity.ok().contentType(MediaType.parseMediaType(CommonConstants.EXCEL_CONTENT_TYPE.get()))
					.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"wallet-upload-format.xlsx\"")
					.body(resource);
		} else {
			log.info("Error ocurred while downloading wallet upload sample file.");

			modelAndView.addObject("wallet_upload_format_error", true);
			return modelAndView;
		}

	}

	/**
	 * @param file MultiPartFile that will receive in the request
	 * @return Object
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/wallet-upload-data")
	public Object uploadWalletData(@RequestParam MultipartFile file) throws IOException {
		log.info("POST/wallet-upload/wallet-upload-data");
		// write the code for ASPOSE
		String excelFileCheckFlag = ExcelFileCheck.scanExcelFile(file.getInputStream());
		log.info("Excel sheet serching for ASPOSE values in  wallet upload upload :{}", excelFileCheckFlag);
		ModelAndView modelAndView = new ModelAndView(ViewLayer.WALLET_UPLOAD_VIEW.get());
		if ("success".equalsIgnoreCase(excelFileCheckFlag) || excelFileCheckFlag.equalsIgnoreCase("0")) {
			Resource resource = service.processUploadFormat(file);
			if (null != resource) {
				log.info("Downloading  wallet upload file iresponse.");
				return ResponseEntity.ok()
						.contentType(MediaType.parseMediaType(CommonConstants.EXCEL_CONTENT_TYPE.get()))
						.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"wallet-upload-response.xlsx\"")
						.body(resource);
			} else {
				log.info("Error ocurred while uploading wallet upload  file.");

				modelAndView.addObject("invalid", true);
				return modelAndView;
			}
		} else {
			log.info("Error ocurred while uploading wallet upload  file.During the excel file serching");

			modelAndView.addObject("invalid", true);
			return modelAndView;
		}
	}
}
