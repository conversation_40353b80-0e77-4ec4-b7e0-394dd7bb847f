package com.mosambee.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mosambee.bean.AcquirerAllowedTxnBean;
import com.mosambee.bean.AcquirerBasicDetailsBean;
import com.mosambee.bean.AcquirerBean;
import com.mosambee.bean.AcquirerGraphBean;
import com.mosambee.bean.AddSbiDetailsMidBean;
import com.mosambee.bean.AddTransactionPostingBean;
import com.mosambee.bean.AdminAcquirerBean;
import com.mosambee.bean.AdminAcquirerDataTablesRequestBean;
import com.mosambee.bean.AdminAcquirerListBean;
import com.mosambee.bean.AdminAcquirerListDataTableBean;
import com.mosambee.bean.AdminAcquirerUsersBean;
import com.mosambee.bean.AdminAcquirerUsersDataTableBean;
import com.mosambee.bean.AdminAcquirerUsersSearchBean;
import com.mosambee.bean.AdminAddAcquirerBean;
import com.mosambee.bean.CustomUser;
import com.mosambee.bean.EditAcquirerDetailsBean;
import com.mosambee.bean.EditSbiDetailsBean;
import com.mosambee.bean.GroupBean;
import com.mosambee.bean.GroupCrudDataTableBean;
import com.mosambee.bean.MerchantMappingBean;
import com.mosambee.bean.ResponseBean;
import com.mosambee.bean.TransactionPostingDataTableBean;
import com.mosambee.bean.datatables.DataTablesRequest;
import com.mosambee.bean.datatables.DataTablesResponse;
import com.mosambee.constants.CommonConstants;
import com.mosambee.constants.ViewLayer;
import com.mosambee.service.AdminAcquirerService;

import lombok.extern.log4j.Log4j2;

/**
 * AdminAcquirerController is basically used to search admin acquirers by dates.
 * We are basically using this controller to show list of acquirers
 * corresponding to particular dates.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 18-March-2020
 */
@Log4j2
@Controller
@RequestMapping("/adminacquirer")
public class AdminAcquirerController {

	private static final String USERS_SEARCH_FORM_BEAN = "usersSearchFormBean";
	private static final String AQUIRERES = "aquirers";
	private static final String CURRENCYLIST = "currencyList";
	private static final String ADDACQ = "addAcquirer";
	private static final String FORMVALIDATION = "formValidation";
	private static final String ADDTRANSACTIONPOSTINGBEAN = "AddTransactionPostingBean";

	@Autowired
	private AdminAcquirerService service;

	/**
	 * API to fetch the view for admin Acquirer list
	 * 
	 * @return String
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/admin-acquirer-view")
	public ModelAndView viewActiveAcquirer() {
		log.info("GET admin-acquirer-view");
		ModelAndView modelAndView = new ModelAndView(ViewLayer.ADMIN_ACQUIRER_VIEW.get());
		List<AcquirerBean> acquirerList = service.getListOfAcquirer();
		modelAndView.addObject(AQUIRERES, acquirerList);
		return modelAndView;
	}

	/**
	 * API to fetch data-tables response for admin acquirer list.
	 * 
	 * @param AdminAcquirerDataTablesRequestBean
	 * @return ResponseEntity<DataTablesResponse<AdminAcquirerBean>>
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_USER') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/admin-acquirer-list")
	public ResponseEntity<DataTablesResponse<AdminAcquirerBean>> viewAdminAcquirerList(
			@RequestBody AdminAcquirerDataTablesRequestBean dtRequest) {
		log.info("POST /admin-acquirer-list {}", dtRequest);

		DataTablesResponse<AdminAcquirerBean> dtResponse = service.getAdminAcquirerList(dtRequest);
		log.info("RESPONSE /admin-acquirer-list {}", dtResponse);
		dtResponse.setDraw(dtRequest.getDtRequest().getDraw());
		return new ResponseEntity<>(dtResponse, HttpStatus.OK);
	}

	/**
	 * API to view Add Acquirer page
	 * 
	 * @return ModelAndView
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/add-acquirer")
	public ModelAndView addAcquirer() {
		log.info("GET /adminacquirer/admin-acquirer");
		ModelAndView modelAndView = new ModelAndView(ViewLayer.ADD_ACQUIRER_VIEW.get());

		Map<Integer, String> currencyMap = service.getCurrencyList();
		log.info("currencyMap: {}", currencyMap);
		modelAndView.addObject(CURRENCYLIST, currencyMap);
		modelAndView.addObject(FORMVALIDATION, ADDACQ);
		return modelAndView;
	}

	/**
	 * API to Add Acquirer Details.
	 * 
	 * @param bean {@link AddSbiDetailsMidBean}
	 * @return ModelAndView
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/add-acquirer")
	public ModelAndView addAcquirer(@ModelAttribute AdminAddAcquirerBean bean) {
		log.info("POST /adminacquirer/add-acquirer with bean as: {}", bean);

		ModelAndView modelAndView = new ModelAndView(ViewLayer.ADD_ACQUIRER_VIEW.get());

		boolean acquirerExists = service.checkAcquirerExist(bean.getName());

		if (acquirerExists) {
			log.info("Acquirer already exists with Name: {}", bean.getName());
			modelAndView.addObject("acquirerExists", acquirerExists);

			Map<Integer, String> currencyMap = service.getCurrencyList();

			modelAndView.addObject(CURRENCYLIST, currencyMap);
			modelAndView.addObject(FORMVALIDATION, ADDACQ);
			return modelAndView;
		}

		CustomUser user = (CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		long userId = Long.parseLong(user.getMyMap().get("id"));

		String response = service.addNewAcquirer(bean, userId);

		log.info("RESPONSE: /adminacquirer/add-acquirer Response: {}", response);

		if (response.equalsIgnoreCase("Acquirer Added Successfully")) {
			modelAndView.addObject("msg", true);
		} else {
			modelAndView.addObject("msg", response);
		}

		Map<Integer, String> currencyMap = service.getCurrencyList();

		modelAndView.addObject(CURRENCYLIST, currencyMap);
		modelAndView.addObject(FORMVALIDATION, ADDACQ);
		return modelAndView;
	}

	/**
	 * API to View the List of Acquirers
	 * 
	 * @return ModelAndView
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/list-acquirer")
	public ModelAndView getAcquirerList() {
		log.info("GET /adminacquirer/list-acquirer");
		ModelAndView modelAndView = new ModelAndView(ViewLayer.LIST_ACQUIRER_VIEW.get());
		List<AcquirerBean> acquirerList = service.getListOfAcquirer();
		modelAndView.addObject(AQUIRERES, acquirerList);
		return modelAndView;
	}

	/**
	 * API to fetch data-tables response for Acqurier Listing.
	 * 
	 * @param dtRequest {@link DataTablesRequest}
	 * 
	 * @return ResponseEntity<DataTablesResponse<AdminAcquirerListBean>>
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/list-acquirer")
	public ResponseEntity<DataTablesResponse<AdminAcquirerListBean>> getAcquirerList(
			@RequestBody AdminAcquirerListDataTableBean dtRequest) {

		log.info("POST /adminacquirer/list-acquirer  with dtRequest: {}", dtRequest);

		DataTablesResponse<AdminAcquirerListBean> dtResponse = service.getSiteAcquirerList(dtRequest);
		dtResponse.setDraw(dtRequest.getDataTablesParameter().getDraw());
		return new ResponseEntity<>(dtResponse, HttpStatus.OK);
	}

	/**
	 * API to fetch data for editing Acquirer Listing.
	 * 
	 * @param editBeanData {@link EditAcquirerDetailsBean}
	 * @return ModelAndView
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/list-acquirer-edit")
	public ModelAndView editAcquirerDetails(@ModelAttribute EditAcquirerDetailsBean editBeanData) {
		log.info("POST /adminacquirer/list-acquirer-edit  with listBean as: {}", editBeanData);
		ModelAndView modelAndView = null;

		long acqId = editBeanData.getAcqId();

		EditAcquirerDetailsBean beanData = service.getSiteAcquirerData(acqId);
		log.info("beanData as: {}", beanData);

		modelAndView = new ModelAndView(ViewLayer.ADD_ACQUIRER_VIEW.get());

		Map<Integer, String> currencyMap = service.getCurrencyList();

		modelAndView.addObject(CURRENCYLIST, currencyMap);
		modelAndView.addObject("beanData", beanData);
		return modelAndView;

	}

	/**
	 * API to Edit/Update the fetched data-tables response for Acquirer Listing.
	 * 
	 * @param addBeanData {@link EditSbiDetailsBean}
	 * @return ModelAndView
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/update-acquirer")
	public ModelAndView updateAcquirerDetails(@ModelAttribute EditAcquirerDetailsBean updateBeanData) {
		log.info("POST /adminacquirer/update-acquirer  with updateBean as: {}", updateBeanData);
		ModelAndView modelAndView = null;

		long acqId = updateBeanData.getAcqId();
		log.info("acqId as: {}", acqId);

		CustomUser user = (CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		long userId = Long.parseLong(user.getMyMap().get("id"));

		modelAndView = new ModelAndView(ViewLayer.ADD_ACQUIRER_VIEW.get());

		String response = service.updateAcquirer(updateBeanData, acqId, userId);

		if (response.equalsIgnoreCase("success")) {
			modelAndView.addObject("msg", "updated");
		} else {
			modelAndView.addObject("msg", response);
		}

		Map<Integer, String> currencyMap = service.getCurrencyList();
		modelAndView.addObject(CURRENCYLIST, currencyMap);
		modelAndView.addObject("beanData", updateBeanData);
		return modelAndView;

	}

	/**
	 * API to process Acquirer User data for listing
	 * 
	 * @return ModelAndView
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_USER') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/acquirer-user-list")
	public ModelAndView processAcquirerUsersView(AdminAcquirerUsersSearchBean bean) {

		log.info("POST /adminacquirer/acquirer-user-list  with AdminAcquirerUsersSearchBean as: {}", bean);

		ModelAndView modelAndView = new ModelAndView(ViewLayer.LIST_ACQUIRER_USERS.get());
		modelAndView.addObject(USERS_SEARCH_FORM_BEAN, bean);
		return modelAndView;
	}

	/**
	 * API to get Acquirer User list data based on search results and txn type
	 * 
	 * @param dtRequest
	 * @return ResponseEntity<DataTablesResponse<AdminAcquirerUsersBean>>
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_USER') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/get-users-list-data")
	public ResponseEntity<DataTablesResponse<AdminAcquirerUsersBean>> getUsersListData(
			@RequestBody AdminAcquirerUsersDataTableBean dtRequest) {
		log.info("post /get-users-list-data {}", dtRequest);

		String searchValue = dtRequest.getDtRequest().getSearch().getValue();
		
		log.info("Search Value: {}", searchValue);
		DataTablesResponse<AdminAcquirerUsersBean> dtResponse = service.getAcquirerUserListData(dtRequest);
		dtResponse.setDraw(dtRequest.getDtRequest().getDraw());

		return new ResponseEntity<>(dtResponse, HttpStatus.OK);
	}

	/**
	 * download process excel request of merchant list
	 * 
	 * @param userBean
	 * @param result
	 * @return Object
	 */
	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/download-excel")
	public Object downloadAcqUserExcel(@ModelAttribute("userSearchBean") AdminAcquirerUsersDataTableBean userBean,
			BindingResult result) {
		log.info("POST /adminacquirer/download-excel AdminAcquirerUsersDataTableBean {}", userBean);

		if (result.hasErrors()) {
			log.info(" error occured while validating user");
			return new ModelAndView(ViewLayer.LIST_ACQUIRER_USERS.get());
		}

		Resource resource = service.downloadUserExcel(userBean);

		if (resource != null) {
			return ResponseEntity.ok().contentType(MediaType.parseMediaType(CommonConstants.EXCEL_CONTENT_TYPE.get()))
					.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"User_List.xlsx\"").body(resource);
		} else {

			ModelAndView modelAndView = new ModelAndView(ViewLayer.LIST_ACQUIRER_USERS.get());
			modelAndView.addObject("excelfail", true);
			modelAndView.addObject(USERS_SEARCH_FORM_BEAN, userBean);
			return modelAndView;
		}

	}

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_USER') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/acquirer-profile")
	public ModelAndView getAcquirerProfileView() {
		log.info("GET /adminacquirer/acquirer-profile");
		ModelAndView modelAndView = new ModelAndView(ViewLayer.ACQUIRER_PROFILE.get());
		List<AcquirerBean> acquirerList = service.getListOfAcquirer();
		modelAndView.addObject(AQUIRERES, acquirerList);
		return modelAndView;
	}

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_USER') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/acquirer-profile")
	public ModelAndView getAcquirerProfile(@RequestParam String acqId) {

		log.info("POST /adminacquirer/acquirer-profile");
		ModelAndView modelAndView = new ModelAndView(ViewLayer.ACQUIRER_PROFILE.get());

		List<AcquirerBean> acquirerList = service.getListOfAcquirer();
		AcquirerBasicDetailsBean basicDetails = service.getAcquirerProfileInfo(acqId);
		AcquirerAllowedTxnBean secondPage = service.getAllowedTxn(acqId);
		List<AcquirerGraphBean> thirdPage = service.getAcquirerGraphDetails(acqId);
		String listJson = "";
		String txnAmountJson = "";

		if (!thirdPage.isEmpty()) {
			ObjectMapper mapper = new ObjectMapper();
			try {
				listJson = mapper.writeValueAsString(thirdPage);
				txnAmountJson = mapper.writeValueAsString(secondPage.getListTxnAmount());
			} catch (JsonProcessingException e) {
				log.info("Exception occurred in json {}", e);
			}
		}
		modelAndView.addObject(AQUIRERES, acquirerList);
		modelAndView.addObject("acqId", acqId);
		modelAndView.addObject("firstPage", basicDetails);
		modelAndView.addObject("secondPage", secondPage);
		modelAndView.addObject("txnTypesAmount", txnAmountJson);
		modelAndView.addObject("thirdPage", listJson);

		return modelAndView;
	}

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/add-transaction-posting")
	public ModelAndView addTransactionPosting() {
		log.info("GET add-transaction-posting");
		ModelAndView modelAndView = new ModelAndView(ViewLayer.ADD_TRANSACTION_POSTING.get());
		List<AcquirerBean> acquirerList = service.getListOfAcquirer();
		AddTransactionPostingBean bean = new AddTransactionPostingBean();
		modelAndView.addObject(AQUIRERES, acquirerList);
		modelAndView.addObject(ADDTRANSACTIONPOSTINGBEAN, bean);
		return modelAndView;
	}

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/add-transaction-posting")
	public ModelAndView addTransactionPosting(@Valid @ModelAttribute AddTransactionPostingBean bean,
			BindingResult result) {
		log.info("POST /adminacquirer/add-transaction-posting with bean as: {}", bean);

		ModelAndView modelAndView = new ModelAndView(ViewLayer.ADD_TRANSACTION_POSTING.get());
		List<AcquirerBean> acquirerList = service.getListOfAcquirer();
		if (result.hasErrors()) {

			List<FieldError> errors = result.getFieldErrors();
			StringBuilder message = new StringBuilder();

			for (FieldError e : errors) {
				message.append(e.getDefaultMessage());
			}
			modelAndView.addObject("error", true);
			modelAndView.addObject("message", message.toString());
			modelAndView.addObject(ADDTRANSACTIONPOSTINGBEAN, bean);
			modelAndView.addObject(AQUIRERES, acquirerList);
			return modelAndView;
		}

		modelAndView.addObject(AQUIRERES, acquirerList);

		CustomUser user = (CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		long userId = Long.parseLong(user.getMyMap().get("id"));

		bean = service.addTransactionPosting(bean, userId, acquirerList);

		log.info("RESPONSE: /adminacquirer/add-transaction-posting Response Bean: {}", bean);
		modelAndView.addObject(ADDTRANSACTIONPOSTINGBEAN, bean);

		return modelAndView;
	}

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@GetMapping("/transaction-posting-view")
	public ModelAndView viewTransactionPosting() {
		log.info("GET /adminacquirer/transaction-posting-view");
		ModelAndView modelAndView = new ModelAndView(ViewLayer.TRANSACTION_POSTING_LIST.get());
		List<AcquirerBean> acquirerList = service.getListOfAcquirer();
		modelAndView.addObject(AQUIRERES, acquirerList);
		return modelAndView;
	}

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_USER') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/transaction-posting-view")
	public ResponseEntity<DataTablesResponse<AddTransactionPostingBean>> transactionPostingView(
			@Valid @RequestBody TransactionPostingDataTableBean dtRequest) {
		log.info("POST /adminacquirer/transaction-posting-view {}", dtRequest);

		CustomUser user = (CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		long userId = Long.parseLong(user.getMyMap().get("id"));

		DataTablesResponse<AddTransactionPostingBean> dtResponse = service.getTransactionPostingView(dtRequest, userId);
		dtResponse.setDraw(dtRequest.getDtRequest().getDraw());
		return new ResponseEntity<>(dtResponse, HttpStatus.OK);

	}

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/edit-transaction-posting")
	public ModelAndView editTransactionPosting(@Valid @RequestParam String tableId) {
		log.info("POST /adminacquirer/edit-transaction-posting  as: {}", tableId);
		ResponseBean response = new ResponseBean();
		ModelAndView modelAndView = new ModelAndView(ViewLayer.UPDATE_TRANSACTION_POSTING.get());

		AddTransactionPostingBean bean = service.getTransactionPostingData(tableId, response);
		log.info("beanData as: {}", bean);

		List<AcquirerBean> acquirerList = service.getListOfAcquirer();

		modelAndView.addObject(AQUIRERES, acquirerList);
		modelAndView.addObject(ADDTRANSACTIONPOSTINGBEAN, bean);
		return modelAndView;

	}

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/update-transaction-posting")
	public ModelAndView updateTransactionPosting(@Valid @RequestParam int flag,
			@ModelAttribute AddTransactionPostingBean bean, BindingResult result) {
		log.info("POST /adminacquirer/update-transaction-posting with bean as: {}", bean);

		ResponseBean response = new ResponseBean();

		ModelAndView modelAndView = new ModelAndView(ViewLayer.UPDATE_TRANSACTION_POSTING.get());
		List<AcquirerBean> acquirerList = service.getListOfAcquirer();
		if (result.hasErrors()) {

			List<FieldError> errors = result.getFieldErrors();
			StringBuilder message = new StringBuilder();

			for (FieldError e : errors) {
				message.append(e.getDefaultMessage());
			}
			log.info("Bean error{}", message.toString());

			modelAndView.addObject(ADDTRANSACTIONPOSTINGBEAN, bean);
			modelAndView.addObject("message", message.toString());
			modelAndView.addObject(AQUIRERES, acquirerList);
			return modelAndView;
		}
		//Flag(1=edit,2=approve,3=reject)
		modelAndView.addObject(AQUIRERES, acquirerList);
		CustomUser user = (CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		long userId = Long.parseLong(user.getMyMap().get("id"));
		if ((flag == 2) || (flag == 3)) {

			bean = service.getTransactionPostingData(bean.getTableId(), response);
			log.info("Transaction Posting data{}", bean);
		}

		response = service.approveTransactionPosting(bean.getTableId(), bean.getShowStatus(), userId, acquirerList,
				flag, bean);
		AddTransactionPostingBean responsebean = service.getTransactionPostingData(bean.getTableId(), response);

		log.info("RESPONSE: /adminacquirer/update-transaction-posting Response: {}", responsebean);
		modelAndView.addObject(ADDTRANSACTIONPOSTINGBEAN, responsebean);
		return modelAndView;
	}

	@PreAuthorize("hasRole('ROLE_SITE_ADMIN') or hasRole('ROLE_SITE_USER') or hasRole('ROLE_SITE_SUPER_ADMIN')")
	@PostMapping("/update-declineTransactionPosting-status")
	@ResponseBody
	public ModelAndView updateDeclineTransactionPostingStatus(@Valid @RequestParam String tableId,@RequestParam int declineTransactionPosting, @RequestParam String acqId) {
		log.info("POST /adminacquirer/update-declineTransactionPosting-status");
		ModelAndView modelAndView = new ModelAndView(ViewLayer.TRANSACTION_POSTING_LIST.get());
		boolean statusResonse = service.updateDeclineTransactionPostingStatus(tableId, declineTransactionPosting,acqId);
		List<AcquirerBean> acquirerList = service.getListOfAcquirer();
		modelAndView.addObject(AQUIRERES, acquirerList);
		modelAndView.addObject("msg", statusResonse);
		modelAndView.addObject("acqId", acqId);
		return modelAndView;

	}

}
