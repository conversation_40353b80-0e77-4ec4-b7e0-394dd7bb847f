package com.mosambee.properties;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import lombok.Data;
import lombok.ToString;

/**
 * ExcelCommonProperties is basically used to get common excel properties from
 * the application environment specific properties file.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 27-December-2019
 */
@Data
@ToString
@Validated
@Component
@PropertySource("classpath:/excel-properties/excel.properties")
@ConfigurationProperties(prefix = "excel-common.props")
public class ExcelCommonProperties {

	private List<String> validExtensionList;
	private List<String> validProgramDomainList;
	private List<String> validCumulativeCountModes;
}
