package com.mosambee.config;

import com.mosambee.properties.WebSecurityProperties;
import com.mosambee.service.impl.UserServiceImpl;
import com.mosambee.util.SHAPasswordEncoder;

import jakarta.servlet.DispatcherType;
import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;

import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.servlet.util.matcher.MvcRequestMatcher;

import org.springframework.security.web.session.SessionInformationExpiredStrategy;

import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import org.springframework.web.servlet.handler.HandlerMappingIntrospector;


@Log4j2
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

    @Autowired
    private AccessDeniedHandler accessDeniedHandler;

    @Autowired
    private AuthenticationSuccessHandler successHandler;

    @Autowired
    private AuthenticationFailureHandler failureHandler;

    @Autowired
    private SessionInformationExpiredStrategy expiredSessionStrategy;

    //@Autowired
    //private CustomAuthenticationProvider customAuthenticationProvider;

    @Autowired WebSecurityProperties webSecurityProperties;

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    protected UserDetailsService userDetailsService() {
        return new UserServiceImpl();
    }


    @Bean
    public PasswordEncoder passwordEncoder() {
        return SHAPasswordEncoder.getInstance();
    }


    @Autowired
    public void configureGlobal(AuthenticationManagerBuilder auth) throws Exception {

        auth
                .userDetailsService(userDetailsService())
                .passwordEncoder(passwordEncoder());
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http , HandlerMappingIntrospector introspector) throws Exception {

        MvcRequestMatcher.Builder mvcMatcherBuilder = new MvcRequestMatcher.Builder(introspector);


        http
                .csrf(csrf -> csrf.ignoringRequestMatchers(mvcMatcherBuilder.pattern("/login"),mvcMatcherBuilder.pattern( "/logout")))
                //  .securityMatcher("/login")

                .authorizeHttpRequests((authorize) -> authorize
                                .dispatcherTypeMatchers(DispatcherType.FORWARD).permitAll()
                                .requestMatchers(

                                        mvcMatcherBuilder.pattern("/webjars/**"),
                                        mvcMatcherBuilder.pattern("/assets/**"),
                                        mvcMatcherBuilder.pattern("/resources/**"),
                                        mvcMatcherBuilder.pattern("/forgot-password"),
                                        mvcMatcherBuilder.pattern("/login"),
                                        mvcMatcherBuilder.pattern("/api/**"),
                                        mvcMatcherBuilder.pattern("/reciept/**"),
                                        mvcMatcherBuilder.pattern("/captcha/**"),
                                        mvcMatcherBuilder.pattern("/otp/**")

                                )

                                .permitAll().anyRequest().authenticated()

                        //  .anyRequest().permitAll()

                ).formLogin(

                        (form) -> form

                                .loginPage("/login")
                                //  .loginProcessingUrl(webSecurityProperties.getLoginPage())
                                .successHandler(successHandler)
                                .failureHandler(failureHandler)
                                //  .defaultSuccessUrl(webSecurityProperties.getDefaultSuccessUrl())
                                .permitAll()

                ).logout(

                        (logout) -> logout

                                .logoutUrl(webSecurityProperties.getLogoutUrl())
                                .logoutSuccessUrl(webSecurityProperties.getLogoutSuccessUrl())
                                .deleteCookies(webSecurityProperties.getDeleteCookies())
                                .invalidateHttpSession(false)
                                .permitAll()

                ).exceptionHandling(

                        (exceptionHandlingCustomizer) -> exceptionHandlingCustomizer.accessDeniedHandler(accessDeniedHandler)

                ).sessionManagement(

                        (sessionManagementCustomizer) -> sessionManagementCustomizer

                                .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                                .sessionFixation()
                                .migrateSession()
                                .invalidSessionUrl(webSecurityProperties.getInvalidSessionUrl())
                                .maximumSessions(1)
                                .expiredSessionStrategy(expiredSessionStrategy)


                ).cors(corsCustomizer -> corsCustomizer.configurationSource(corsConfigurationSource(webSecurityProperties))
                );


        return http.build();

        //return http.authorizeHttpRequests((auth) -> auth.anyRequest().permitAll()).build();
    }




    public CorsConfigurationSource corsConfigurationSource(WebSecurityProperties webSecurityProperties) {
        final CorsConfiguration configuration = new CorsConfiguration();
        log.info("Origins :{} ", webSecurityProperties.getOrigins());
        configuration.setAllowedOriginPatterns(webSecurityProperties.getOrigins());
        configuration.setAllowedMethods(webSecurityProperties.getMethods());
        configuration.setAllowedHeaders(webSecurityProperties.getHeaders());
        configuration.setAllowCredentials(true);
        final UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }



}

