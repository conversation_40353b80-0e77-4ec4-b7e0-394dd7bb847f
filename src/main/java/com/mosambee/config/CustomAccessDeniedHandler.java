package com.mosambee.config;

import java.io.IOException;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import com.mosambee.util.CommonUtils;

import lombok.extern.log4j.Log4j2;

/**
 * CustomAccessDeniedHandler is used to handle 403 page. Here we are
 * implementing AccessDeniedHandler which we are auto-wiring in
 * WebSecurityConfig.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 12-December-2019
 */
@Log4j2
@Component
public class CustomAccessDeniedHandler implements AccessDeniedHandler {

	@Autowired
	private CommonUtils commonUtils;

	@Override
	public void handle(HttpServletRequest request, HttpServletResponse response,
			AccessDeniedException accessDeniedException) throws IOException, ServletException {

		log.error("accessDeniedException: {}", accessDeniedException);
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();

		if (auth != null) {
			log.info("User {} attempted to access protected URI: {}, having user-details as {}", auth.getName(),
					request.getRequestURI(), auth);
			commonUtils.clearSecurityContextHolderAndInvalidateSession(request);
		}

		response.sendRedirect(request.getContextPath() + "/login?forbidden");

	}

}
