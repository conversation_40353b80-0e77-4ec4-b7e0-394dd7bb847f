package com.mosambee.dao;

import java.util.Map;

import com.mosambee.bean.ApiPasswordBean;
import com.mosambee.bean.ApiPasswordDataBean;
import com.mosambee.bean.ResponseBean;
import com.mosambee.bean.UpdateAPIPaswordConfigRequestFromList;
import com.mosambee.bean.datatables.DataTablesResponse;

public interface ApiPasswordDao {

	public DataTablesResponse<ApiPasswordBean> getActiveApiPassword(ApiPasswordDataBean dtRequest,
			String orderingColumnName, Map<String, String> searchMap);
	
	public boolean updateApi(ApiPasswordBean updateApi, ResponseBean responseBean);
	
	public boolean updateApiPassword(UpdateAPIPaswordConfigRequestFromList updateAPIPaswordConfigRequestFromList,
			ApiPasswordBean createAPIGroup, ResponseBean responseBean);
	
}
