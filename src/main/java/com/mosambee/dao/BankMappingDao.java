package com.mosambee.dao;
import java.util.List;

import com.mosambee.bean.BankMappingBean;
import com.mosambee.bean.BankMappingDataBean;
import com.mosambee.bean.EmailSMSNotificationbean;
import com.mosambee.bean.MerchanAddressBean;
import com.mosambee.bean.MerchantListBean;

/**
 * MerchantSpecificDao is specification for {@link MerchantSpecificDaoImpl}
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 10-February-2021
 */
public interface BankMappingDao {


	
	String  getBankMappingViewLastTransactionTime(String terminalId,String username);

	List<MerchantListBean>getAcquirerByTerminal(String terminalId);

	List<EmailSMSNotificationbean> emailsmslog(String terminalId, String acquirer,
			List<EmailSMSNotificationbean> emailsms, String type);

	BankMappingBean getBankMappingFromTerminalDetails(BankMappingDataBean bean);

	BankMappingBean getBankMappingFromMMUT(BankMappingDataBean dtRequest);

	MerchanAddressBean getBankMappingViewMerchantAddress(String merchantAddressId);

	List<MerchantListBean> getAcquirerByMMUT(String terminalId);

	
}
