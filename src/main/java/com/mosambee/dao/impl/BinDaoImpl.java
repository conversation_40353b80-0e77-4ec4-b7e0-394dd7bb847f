package com.mosambee.dao.impl;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Repository;

import com.mosambee.bean.BinDataTableRequestBean;
import com.mosambee.bean.BinNewBean;
import com.mosambee.bean.CreateBinBean;
import com.mosambee.bean.CustomUser;
import com.mosambee.bean.IssuerBean;
import com.mosambee.bean.datatables.DataTablesResponse;
import com.mosambee.dao.BinDao;

import lombok.extern.log4j.Log4j2;


/**
 * {@link BinDaoImpl } is responsible for handling database operations for BIN
 * module. The class is responsible for fetching the bin list based on provided
 * dates.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 22-01-2021
 */
@Log4j2
@Repository("BinDao")
public class BinDaoImpl implements BinDao {

	@Autowired
	@Qualifier("masterSfnVasTemplate")
	private JdbcTemplate masterSfnVasTemplate;

	@Autowired
	@Qualifier("slaveSfnVasTemplate")
	private JdbcTemplate slaveSfnVasTemplate;

	
	/**
	 * getListOfIssuer is used to get list of issuer
	 * 
	 * @return List<IssuerBean>
	 */
	@Override
	public List<IssuerBean> getListOfIssuer() {
		List<IssuerBean> issuerBeanList = new ArrayList<>();

		String sql = "{ call tsp_web_admin_slave_getIssuerList() }";

		try (Connection connection = this.slaveSfnVasTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sql)) {

			log.info("getListOfIssuer {}", callableStatement);
			try (ResultSet resultSet = callableStatement.executeQuery()) {

				while (resultSet.next()) {
					IssuerBean issuerBean = new IssuerBean();
					issuerBean.setId(resultSet.getInt(1));
					issuerBean.setName(resultSet.getString(2));
					issuerBeanList.add(issuerBean);
				}

				log.info("getListOfIssuer {}", issuerBeanList);
			}
		} catch (Exception e) {
			log.error("error occured on getListOfIssuer {} ", e);
		}

		return issuerBeanList;
	}

	/**
	 * checkBinExists is used to check binValue
	 * 
	 * @param binValue
	 * @return boolean
	 * 
	 */
	@Override
	public boolean checkBinExists( CreateBinBean createBinBean) {
		String sqlQuery = "{ call tsp_web_admin_slave_checkBin(?,?,?) }";
		try (Connection connection = this.slaveSfnVasTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sqlQuery)) {

			callableStatement.setInt(1, createBinBean.getBinValue());
			callableStatement.setInt(2, createBinBean.getCardType());

			callableStatement.registerOutParameter(3, java.sql.Types.INTEGER);
			log.info("checkBinExists: {}", callableStatement);
			callableStatement.executeQuery();
			log.info("checkBinExists: {}", callableStatement);
			int message = callableStatement.getInt(3);

			if (message == 1) {
				return true;
			} 

		} catch (Exception e) {
			log.error("Exception occurred in checkBinExists {}", e.getMessage());
		}
		return false;

	}

	/**
	 * addBin is used to add new bin
	 * 
	 * @param CreateBinBean
	 *            {@link createBinBean}
	 * @return boolean
	 */
	@Override
	public String addBin(@Valid CreateBinBean createBinBean) {
		String result = "";

		String sqlQuery = "{ call tsp_web_admin_master_addBin(?,?,?,? ,?,?,?) }";

		CustomUser user = (CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		String userId = user.getMyMap().get("id");
		try (Connection connection = this.masterSfnVasTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sqlQuery)) {
			log.info("bean: {}", createBinBean);
			
			callableStatement.setInt(1, Integer.parseInt(createBinBean.getIssuer()));
			callableStatement.setInt(2, createBinBean.getBinValue());
			callableStatement.setInt(3, createBinBean.getCardType());
			callableStatement.setInt(4, createBinBean.getBinStatus());
			callableStatement.setString(5, createBinBean.getDescription());
			callableStatement.setBigDecimal(6, new BigDecimal(userId));
			
			callableStatement.registerOutParameter(7, java.sql.Types.INTEGER);
			
			log.info("add addBin callableStatement {}", callableStatement);
			callableStatement.execute();
			log.info("Message from Procedure: {} ",callableStatement.getInt(7));
			
			if (callableStatement.getInt(7) == 1) {
				
				result = "Inserted";
				
			} else {
				
				result = "Updated";
			} 
			
			
		} catch (Exception e) {
			result = "Unable to add/update Bin.!";
			log.error("Exception occurred in addBin {}", e);
		}

		return result;
	}

	/**
	 * getBinList(...) is responsible for getting the bin list, corresponding to the
	 * coming data-tables request. Here we have two parameters, first is the actual
	 * {@link BinDataTableRequestBean}, second one is the orderingColumnName in
	 * which ordering is going to happen (ASC/DESC).
	 * 
	 * @param dtRequest
	 *            {@link BinDataTableRequestBean}
	 * @param orderingColumnName
	 *            {@link String}
	 */
	@Override
	public DataTablesResponse<BinNewBean> getBinList(@Valid BinDataTableRequestBean dtRequest,
			String orderingColumnName, Map<String, String> searchMap) {
		log.info("getting bin info list :{}", dtRequest);
		String sqlQuery = "{call tsp_web_admin_slave_getAllBinList(?,?,?,?,?,?,?,?,?)}";
		DataTablesResponse<BinNewBean> dtResponse = new DataTablesResponse<>();
		List<BinNewBean> list = new ArrayList<>();

		try (Connection connection = this.slaveSfnVasTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sqlQuery)) {

			callableStatement.setInt(1, dtRequest.getDtRequest().getStart());
			callableStatement.setInt(2, dtRequest.getDtRequest().getLength());
			callableStatement.setString(3, orderingColumnName);
			callableStatement.setString(4, dtRequest.getDtRequest().getOrder().get(0).getDir().toString());
			callableStatement.setInt(5, dtRequest.getBinValue());
			callableStatement.setInt(6, dtRequest.getIssuerId());
			callableStatement.setInt(7, dtRequest.getCardType());
			callableStatement.setInt(8, dtRequest.getBinStatus());

			callableStatement.registerOutParameter(9, java.sql.Types.INTEGER);
			log.info("{}", callableStatement);
			try (ResultSet resultSet = callableStatement.executeQuery()) {
				log.info("{}", callableStatement);

				while (resultSet.next()) {

					BinNewBean bean = BinNewBean.builder().binValue(resultSet.getLong(1)).issuerId(resultSet.getLong(2))
							.cardType(resultSet.getInt(3)).binStatus(resultSet.getInt(4)).name(resultSet.getString(5))
							.build();

					list.add(bean);
				}

				log.info("Size of user list is: {}", list.size());

				int totalRecordCount = callableStatement.getInt(9);
				log.info("totalRecordCount is: {}", totalRecordCount);
				dtResponse.setData(list);
				dtResponse.setRecordsFiltered(totalRecordCount);
				dtResponse.setRecordsTotal(totalRecordCount);
			}

		} catch (Exception e) {
			log.error("Exception occurred in getBinList {}", e);
			return null;
		}

		return dtResponse;
	}

	/**
	 * getBinDetails is used to get bin details
	 * 
	 * @param binvale
	 *            {@link String}
	 * @return CreateBinBean
	 */
	@Override
	public CreateBinBean getBinDetails(int binValue) {
		log.info("Inside Bin details value of binValue {} ", binValue);

		String sqlQuery = "{ call tsp_web_admin_slave_getBinDetails(?) }";

		CreateBinBean userCrudBean = new CreateBinBean();

		try (Connection connection = this.slaveSfnVasTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sqlQuery)) {

			callableStatement.setBigDecimal(1, new BigDecimal(binValue));

			try (ResultSet rs = callableStatement.executeQuery()) {

				while (rs.next()) {
					userCrudBean.setBinValue(rs.getInt(1));
					userCrudBean.setIssuerId(rs.getInt(2));
					userCrudBean.setCardType(rs.getInt(3));
					userCrudBean.setBinStatus(rs.getInt(4));
					userCrudBean.setName(rs.getString(5));
				}

			}

			log.info("found Bin details {} ", userCrudBean);
		} catch (Exception e) {

			log.error("Exception occurred in getBinDetails {}", e.getMessage());
		}

		return userCrudBean;
	}

	/**
	 * updateBin is used to update existing user
	 * 
	 * @param CreateBinBean
	 *            {@link CreateBinBean}
	 * @return boolean
	 */
	@Override
	public boolean updateBin(@Valid CreateBinBean createBinBean) {
		boolean result = true;

		String sqlQuery = "{ call tsp_web_admin_master_updateInformationOfBin(?,?,?,?,?) }";
		CustomUser user = (CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		String userId = user.getMyMap().get("id");

		try (Connection connection = this.masterSfnVasTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sqlQuery)) {
			callableStatement.setInt(1, createBinBean.getBinValue());
			callableStatement.setInt(2, createBinBean.getIssuerId());
			callableStatement.setInt(3, createBinBean.getCardType());
			callableStatement.setInt(4, createBinBean.getBinStatus());

			callableStatement.setBigDecimal(5, new BigDecimal(userId));

			callableStatement.execute();

			log.info("update bin {}", callableStatement);

		} catch (Exception e) {
			result = false;
			log.error(" Exception occurred in updateBin {}", e.getMessage());
		}

		return result;
	}

}
