package com.mosambee.dao.impl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.mosambee.bean.AcquirerSettlementListBean;
import com.mosambee.bean.AcquirerSettlementUpdateBean;
import com.mosambee.bean.AdminAcquirerSettlementListDataTableBean;
import com.mosambee.bean.datatables.DataTablesResponse;
import com.mosambee.dao.AcquirerSettlementDao;

import lombok.extern.log4j.Log4j2;

/**
 * {@link AdminAcquirerDaoImpl} is responsible for handling database operations
 * for admin-acquirer module. The class is responsible for fetching the acquirer
 * list based on provided dates.
 * 
 * <AUTHOR>
 * <AUTHOR>
 * @version 1.0
 * @since 18-March-2020
 */
@Log4j2
@Repository("acquirerSettlementDao")
public class AcquirerSettlementDaoImpl implements AcquirerSettlementDao {

	@Autowired
	@Qualifier("slaveSFNTransactionTemplate")
	private JdbcTemplate slaveSFNTransactionTemplate;

	@Autowired
	@Qualifier("masterSFNTransactionTemplate")
	private JdbcTemplate masterSFNTransactionTemplate;

	@Override
	public DataTablesResponse<AcquirerSettlementListBean> getSiteAcquirerSettlementList(
			AdminAcquirerSettlementListDataTableBean dtRequest, String orderingColumnName) {
		DataTablesResponse<AcquirerSettlementListBean> dtResponse = new DataTablesResponse<>();

		List<AcquirerSettlementListBean> list = new ArrayList<>();

		String sqlQuery = "{ call tsp_web_admin_slave_getSiteAcquirerSettlementList(?,?,?,?,?,?) }";

		try (Connection connection = this.slaveSFNTransactionTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sqlQuery)) {

			callableStatement.setInt(1, dtRequest.getDataTablesParameter().getStart());
			callableStatement.setInt(2, dtRequest.getDataTablesParameter().getLength());
			callableStatement.setString(3, orderingColumnName);
			callableStatement.setString(4, dtRequest.getDataTablesParameter().getOrder().get(0).getDir().toString());
			callableStatement.setInt(5, dtRequest.getAcquirer());

			callableStatement.registerOutParameter(6, java.sql.Types.INTEGER);

			try (ResultSet resultSet = callableStatement.executeQuery()) {

				log.info("{}", callableStatement);
				while (resultSet.next()) {

					AcquirerSettlementListBean bean = new AcquirerSettlementListBean();

					bean.setId(resultSet.getInt(1));
					bean.setAcquirerName(resultSet.getString(2));
					bean.setSettlementTime(resultSet.getString(3));
					bean.setIsEnable(resultSet.getInt(4));

					list.add(bean);
				}

			}

			log.info("Size of site acquirer settlement list: {}", list.size());

			int totalRecordCount = callableStatement.getInt(6);

			log.info("totalRecordCount: {}", list.size());

			dtResponse.setData(list);
			dtResponse.setRecordsFiltered(totalRecordCount);
			dtResponse.setRecordsTotal(totalRecordCount);
		} catch (Exception e) {
			log.error("Exception occurred in getSiteAcquirerSettlementList {}", e);
			return null;
		}

		dtResponse.setData(list);
		log.info(dtResponse);
		return dtResponse;

	}

	@Override
	public boolean updateAcquirerStatus(int isEnable, String acquirerName) {
		log.info("isEnable {} id {}", isEnable, acquirerName);
		boolean response = true;
		String sqlQuery2 = "{ call tsp_web_admin_master_updateAdminStatus(?,?) }";

		try (Connection connection = this.masterSFNTransactionTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sqlQuery2)) {

			callableStatement.setString(1, acquirerName);
			callableStatement.setInt(2, isEnable);

			callableStatement.execute();
			log.info("updateAcquirerStatus {}", callableStatement);
		} catch (Exception e) {
			response = false;
			log.info("Exception occurred in updateAcquirerStatus {}", e);
		}

		return response;
	}

	@Override
	public String updateSettlementStatus(AcquirerSettlementUpdateBean bean) {
		String response = "";
		String sqlQuery = "{ call tsp_web_admin_master_updateAcquirerSettlementStatus(?,?) }";
		int count;

		try (Connection connection = this.masterSFNTransactionTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sqlQuery)) {

			callableStatement.setString(1, bean.getAcquirerName());
			callableStatement.setLong(2, bean.getIsEnable());

			log.info("callableStatement: {}", callableStatement);
			count = callableStatement.executeUpdate();
			log.info("callableStatement:  {}", callableStatement);
			
			if(count>0) {
				response="updated";
			}
				
			
		} catch (Exception e) {
			response = "Unable to Update Status";
			log.error("Exception occurred in updateSettlementStatus {}", e);
		}

		return response;
	}

	@Override
	public String updateSettlementTime(AcquirerSettlementUpdateBean bean) {
		String response = "";
		String sqlQuery = "{ call tsp_web_admin_master_updateAcquirerSettlementTime(?,?) }";
		int count;

		try (Connection connection = this.masterSFNTransactionTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall(sqlQuery)) {

			callableStatement.setString(1, bean.getAcquirerName());
			callableStatement.setLong(2, bean.getUpdatedTime());

			log.info("callableStatement:  {}", callableStatement);
			count = callableStatement.executeUpdate();
			log.info("callableStatemnt: {}", callableStatement);
			
			if(count>0) {
				response="timeUpdated";
			}
				
			
		} catch (Exception e) {
			response = "Unable to Update Time";
			log.error("Exception occurred in updateSettlementTime {}", e);
		}

		return response;
	}


}
