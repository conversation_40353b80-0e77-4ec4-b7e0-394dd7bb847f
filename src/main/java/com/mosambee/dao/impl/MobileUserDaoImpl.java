package com.mosambee.dao.impl;

import java.security.SecureRandom;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Random;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.mosambee.bean.MobileUserBean;
import com.mosambee.bean.MobileUserDataBean;
import com.mosambee.bean.datatables.DataTablesResponse;
import com.mosambee.dao.MobileUserDao;
import com.mosambee.service.AESService;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Repository(value = "mobileUserDao")
public class MobileUserDaoImpl implements MobileUserDao{

	@Autowired
	@Qualifier("masterSFNTransactionTemplate")
	private JdbcTemplate masterSFNTransactionTemplate;
	

	@Autowired
	@Qualifier("slaveSFNTransactionTemplate")
	private JdbcTemplate slaveSFNTransactionTemplate;
	
	@Autowired
	AESService aesService;
	
	@Override
	public DataTablesResponse<MobileUserBean> getMobileUser(MobileUserDataBean dtRequest) {
		DataTablesResponse<MobileUserBean> dtResponse = new DataTablesResponse<>();
		List<MobileUserBean> list = new ArrayList<>();
		try (Connection connection = this.slaveSFNTransactionTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall("{ call tsp_web_admin_slave_getMobileUser(?) }")) {

			
			callableStatement.setString(1, dtRequest.getUser());
			
			log.info("{}", callableStatement);
			try (ResultSet resultSet = callableStatement.executeQuery()) {

				log.info("{}", callableStatement);
				while (resultSet.next()) {
					
					String pass = resultSet.getString(2);
					String expiryTime = resultSet.getString(3);
					if(pass==null && expiryTime==null) {
						SecureRandom rand = new SecureRandom();
						pass="%04d".formatted(rand.nextInt(10000));				 
						expiryTime= date();
						boolean result = setDfaultPass(pass, expiryTime, resultSet.getInt(1));
						log.info("Data inserted in Db : {}", result);
					}else if(compareDate(expiryTime)){
						pass=aesService.decrypt(resultSet.getString(2))+" (Expired)";
						expiryTime=resultSet.getString(3);
					}else {
						pass=aesService.decrypt(resultSet.getString(2));
						expiryTime=resultSet.getString(3);
					}
					MobileUserBean bean = MobileUserBean.builder().id(resultSet.getInt(1)).userId(resultSet.getInt(1)).user(dtRequest.getUser())
							.pass(pass).expiryTime(expiryTime)
							.build();

					list.add(bean);
				}

			}

			log.info("Size of active user list: {}", list.size());

			
		} catch (Exception e) {
			log.error("Exception occurred in getMobileUser {}", e.getMessage());
			return null;
		}
		dtResponse.setData(list);
		log.info(dtResponse);
		return dtResponse;
	}
	
	@Override
	public boolean setDfaultPass(String pass, String expTime, long id) {
		boolean status=false;
		log.info("User Id is : {}", id);
		try (Connection connection = this.masterSFNTransactionTemplate.getDataSource().getConnection();
				CallableStatement callableStatement = connection.prepareCall("{ call tsp_web_admin_master_setPassExp(?,?,?) }")) {

			callableStatement.setString(1, aesService.encrypt(pass));
			callableStatement.setString(2, expTime);
			callableStatement.setLong(3, id);
			
			log.info("{}", callableStatement);
			int result = callableStatement.executeUpdate();
			if(result==1) {
				status=true;
			}
		} catch (Exception e) {
			log.error("Exception occurred in getMobileUser {}", e.getMessage());
			return false;
		}
		return status;
	}
	
	public String date() {
		Date dt = new Date();
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.HOUR, +1);
		Date date = cal.getTime();
		log.info("Date : {}",date.toString());
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
		return dateFormat.format(date);
	}
	
	public boolean compareDate(String dt) {
		try {
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = new Date();
		Date expDate;
		
			expDate = dateFormat.parse(dt);
		log.info("Date : {}, Expiry Date : {}", date,expDate);
		log.info("Date comparison result : {}", date.compareTo(expDate));
		if(date.compareTo(expDate) <0) {
			return false;
		}else if(date.compareTo(expDate)==0){// both date are same
            if(date.getTime() < expDate.getTime()){// not expired
                return false;
            }else if(date.getTime() == expDate.getTime()){//expired
                return true;
            }
		}else{
			//expired
		    return true;
		}
		} catch (ParseException e) {
			log.info("Date Parsing Exception : {}", e.getMessage());
			return false;
		}
		return false;
		
	}
}
