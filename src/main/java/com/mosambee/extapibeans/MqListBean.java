package com.mosambee.extapibeans;

import com.mosambee.controller.MqController;

import lombok.Data;

/**
 * MqListBean is basically used to carry response parameter for getMqList method of
 * {@link MqController}
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 10-April-2021
 */
@Data
public class MqListBean {

	
	private String srNo;

	private String mountPoint;
	private String deviceSerialNo; 
	private String userName;
	private String publishAcl;
	private String subscribeAcl;
	
	private String deviceId;
	private String deviceToken;
	private String terminalType;
	private String tid;
}
