package com.mosambee.bean;

import com.mosambee.constants.CommonConstants;
import lombok.Data;

@Data
public class VpaBean {
	private long id;
	private String vpa;
	private String qrString;
	private String status;
	private String recordCreated;
	private long createdUserId;
	private String recordUpdated;
	private long updatedUserId;
	private String transactionReference;
	private String tgName;

	public void appendStatus(String status) {
		StringBuilder builder = new StringBuilder();
		builder.append(this.status);
		builder.append(CommonConstants.SPACE.get());
		builder.append(status);
		this.status = builder.toString();
	}
}