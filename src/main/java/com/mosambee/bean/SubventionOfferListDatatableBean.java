package com.mosambee.bean;

import jakarta.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;

import com.mosambee.bean.datatables.DataTablesRequest;
import com.mosambee.controller.SubventionController;

import lombok.Data;

/**
 * SubventionOfferListDatatableBean is basically used to carry request parameter from {@link SubventionController} 
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 04-March-2021
 */
@Data
@Validated
public class SubventionOfferListDatatableBean {

	private DataTablesRequest dataTablesParameter;
	
	private String subRuleId;
	
	@NotNull
	private String fromDate;
	
	@NotNull
	private String toDate;
	
	private String fromRule;
}
