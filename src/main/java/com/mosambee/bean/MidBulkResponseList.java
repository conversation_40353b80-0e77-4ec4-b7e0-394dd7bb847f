package com.mosambee.bean;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * MidApiGroupResponse basically used to represent the parsed row data .
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 28-January-2020
 */
@Data
@SuperBuilder
@NoArgsConstructor
public class MidBulkResponseList {

	@JsonProperty(value = "Ack")
	List<MidApiGroupResponse> ack;
}
