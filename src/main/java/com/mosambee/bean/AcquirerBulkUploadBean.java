package com.mosambee.bean;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * EmiBulkUploadBean is responsible for carrying the data-tables request and
 * response. Mainly to display the list of Data Regarding bulk upload.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 11-February-2020
 */
@SuperBuilder
@ToString
@NoArgsConstructor
@Data
public class AcquirerBulkUploadBean {

	private long acqId;
	private String acquirer;
	private String matcode;
	private long srNo;
	private String transactionTypes;
}
