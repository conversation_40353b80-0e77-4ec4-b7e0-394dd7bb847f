package com.mosambee.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 * @date 16-May-2022
 */
@NoArgsConstructor
@Data
public class BinDetailsDownloadBean {

	private String bin;
	private String binlow;
	private String binhigh;
	private String cardType;
	private String productId;
	private String cardBrand;
	private String countryCodeNum;
	private String type;
	private String issuerBankName;
	private String issuerCountry;
	private String fullCardType;
	private String binType;
	private String typeName;
	private int cardTypeId;
	private int id;
	private int createdBy;
	private String createdTime;
	private int approvedBy;
	private String approvedTime;
	private int rejectedBy;
	private String rejectedTime;
	
}
