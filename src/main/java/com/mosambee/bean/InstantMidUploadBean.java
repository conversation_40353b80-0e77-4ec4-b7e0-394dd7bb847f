package com.mosambee.bean;

import com.mosambee.constants.CommonConstants;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * This class holds row data information in instant mid upload excel response
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class InstantMidUploadBean extends MidDownloadBean {

	private String status;

	/**
	 * Method to append status to existing status.
	 * 
	 * @param status
	 * @return Nothing
	 */
	public void appendStatus(String status) {
		StringBuilder builder = new StringBuilder();
		builder.append(this.status);
		builder.append(CommonConstants.SPACE.get());
		builder.append(status);
		this.status = builder.toString();
	}

}
