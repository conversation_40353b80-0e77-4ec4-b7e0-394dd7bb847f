package com.mosambee.bean;

import com.mosambee.constants.CommonConstants;

import lombok.Data;

@Data
public class SnapbizUploadBean {
	private String posId;
	private String terminalId;
	private String storeId;
	private String programType;
	private String storeIdCreationDate;
	private String status;
	private String reason;
	
	public void appendReason(String reason) {
		StringBuilder builder = new StringBuilder();
		builder.append(this.reason);
		builder.append(CommonConstants.SPACE.get());
		builder.append(reason);
		this.reason = builder.toString();

	}

}
