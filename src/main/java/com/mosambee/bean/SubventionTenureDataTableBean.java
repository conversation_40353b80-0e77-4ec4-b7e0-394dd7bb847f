package com.mosambee.bean;

import org.springframework.validation.annotation.Validated;

import com.mosambee.bean.datatables.DataTablesRequest;

import lombok.Data;

/**
 * This class is Validating enquiryId input field and this is using
 * for datatable request for viewEnqiry list
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Validated
@Data
public class SubventionTenureDataTableBean {
	
	DataTablesRequest dtRequest;
	
	private String enquiryId;
	private String fromDate;
	private String toDate;
	
}
