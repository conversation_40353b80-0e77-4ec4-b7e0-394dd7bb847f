package com.mosambee.bean;

import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class MerchantExcelBean {
	private String businessName;
	private String businessEmail;
	private String address;
	private String poBox;
	private String country;
	private String state;
	private String city;
	private String countryCopy;
	private String stateCopy;
	private String cityCopy;
	private String latitude;
	private String longitude;
	private String contactNo;
	private String mcc;
	private String mdr;
	private String serviceCharges;
	private String chkAllowedDistance;
	private String allowedDistance;
	private String maxTransactionPerHour;
	private String chkMxTransactionPerHour;
	private String chkMaxDailyLimit;
	private String dailyLimit;
	private String chkMaxTxnValue;
	private String maxTransactionValue;
	private String descriptionRequired;
	private String dailyReportRequired;
	private String allowCashCheque;
	private String autoSettlement;
	private String debitOnlySale;
	private String appType;
	private String mosambeeEmi;
	private String kiosk;
	private String chkMaxMonthlyLimit;
	private String monthlyLimit;
	private String card;
	private String acquirer;
	private String tg;
	private String mid;
	private String adminEmail;
	private String adminPhone;
	private String firstName;
	private String lastName;
	private String offlineTransaction;
	private String isEmiAllowed;
	private String ccEmiMode;
	private String ccOffusEmiTxnTid;
	private String ccOnusEmiTxnTid;
	private String dcEmiTxnTid;
	private String matActiveFlag;
	private String mcashPostingFlag;
	private String terminalDetailFlag;
	private String allowedInSmsPay;
	private String instantDiscountFlag;
	private String cashbackFlag;
	private String status;
	private String merchantType;
	private String aggregatorMerchant;
	private String agreementRefNo;
	private String agreementDate;
	private String leadGeneratedBy;
	private String leadClosedBy;
	private String organizationType;
	private String binAllowed;
	private String programType;
	private String companyPan;
	private String businessRented;
	private String businessRentedSince;
	private String websiteName;
	private String gstinNo;
	private String residentialAddress;
	private String residentialCity;
	private String residentialCityCopy;
	private String residentialState;
	private String residentialStateCopy;
	private String residentialPincode;
	private String residentialLandmark;
	private String residentialRented;
	private String residentialRentedSince;
	private String bankName;
	private String branchName;
	private String accountNo;
	private String ifscCode;
	private String accountName;
	private String mainPerson1Name;
	private String mainPerson1Pan;
	private String mainPerson1Aadhaar;
	private String mainPerson1MobNo;
	private String mainPerson1Landline;
	private String mainPerson2Name;
	private String mainPerson2Pan;
	private String mainPerson2Aadhaar;
	private String mainPerson2MobNo;
	private String mainPerson2Landline;
	private String hulcode;
	private String isAgentEnabled;
	private String isNCMCwithSDCard;
	private String isOfflineWithSDCard;
	private String merchantRegistrationMode;
	private String isPushNotificationAllowed;
	private String smspayMultirouting;
	private long posId;
	private String enterpriseCode;
	private String logo;
	private String businessDba;
	private String receiptType;
	private String isTipLimit;
	private String tipLimit;
	private String isSaleComplete;
	private String saleComplete;
	private String applicationtpOn;
	private String receiptIdentifier;
	private String bindingType;
	private String smsPay;
	private String scramble;
	private String matCode;
	private long mappingAcqTgId;
	private int duplicateMidFlag;
	private String isSupervisor;
	private String retailerRegion;
	private String retailerGroup;
	private String postingCode;
	private String postingKey;
	private String receiptAddress;

}