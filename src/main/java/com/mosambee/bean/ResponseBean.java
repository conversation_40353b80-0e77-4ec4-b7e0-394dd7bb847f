package com.mosambee.bean;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This class is used to send as response object to calling jsp or function.
 * 
 * <AUTHOR>
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResponseBean {
	private String msg; // Use this to set message as result of operation.
	private int operationStatus; // Use this to set Operation Status for operation.
	private Map<String, Object> data; // Use this to set any data which we want to send to jsp page.
	private boolean updateRequest; // UUse this member to set next request will be update request or not.
	private String action; // Set form action.

}