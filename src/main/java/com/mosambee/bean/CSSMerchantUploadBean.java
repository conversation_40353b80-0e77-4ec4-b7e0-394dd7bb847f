package com.mosambee.bean;

import com.mosambee.constants.CommonConstants;

import lombok.Data;

@Data
public class CSSMerchantUploadBean {
	private String phoneNo;
	private String status;
	private String reason;
	private long posId;
	private String instantId;
	private String instantUserId;
	
	public void appendReason(String reason) {
		StringBuilder builder = new StringBuilder();
		builder.append(this.reason);
		builder.append(CommonConstants.SPACE.get());
		builder.append(reason);
		this.reason = builder.toString();
	}

}
