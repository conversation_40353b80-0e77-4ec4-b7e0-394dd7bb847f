package com.mosambee.bean;

import jakarta.validation.constraints.NotEmpty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * TempMidTidDownloadCrudBean is used in temp MID TID CRUD operation to hold download data
 * <AUTHOR>
 * @since 15-April-2020
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class TempMidTidDownloadCrudBean {
	
	@NotEmpty(message="Please enter formDate")
	private String fromDate;
	
	@NotEmpty(message="Please enter toDate")
	private String toDate;
}
