package com.mosambee.bean;

import com.mosambee.bean.datatables.DataTablesRequest;
import com.mosambee.controller.WalletController;

import lombok.Data;

/**
 * WalletPriceListDataTableBean is basically used to carry request parameter for viewWalletPriceList method of
 * {@link WalletController} 
 * <AUTHOR>
 * @version 1.0
 * @since 03-November-2020
 */
@Data
public class WalletPriceListDataTableBean {

	private DataTablesRequest dtRequest;

	private String brandCode;
	
}
