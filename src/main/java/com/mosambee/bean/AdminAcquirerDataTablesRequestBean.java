package com.mosambee.bean;

import jakarta.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;

import com.mosambee.bean.datatables.DataTablesRequest;
import com.mosambee.controller.AdminAcquirerController;

import lombok.Data;
import lombok.ToString;

/**
 * AdminAcquirerDataTablesRequestBean is basically used to carry request parameter for viewAdminAcquirerList method of
 * {@link AdminAcquirerController} 
 * <AUTHOR>
 * @version 1.0
 * @since 18-March-2020
 */
@Validated
@ToString
@Data
public class AdminAcquirerDataTablesRequestBean {

	private DataTablesRequest dtRequest;

	@NotNull
	private String acquirer;
	
	@NotNull
	private String fromDate;
	
	@NotNull
	private String toDate;
	
}
