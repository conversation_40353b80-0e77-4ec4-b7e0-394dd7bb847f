package com.mosambee.bean;

import jakarta.validation.constraints.NotNull;
import com.mosambee.bean.datatables.DataTablesRequest;
import lombok.Data;

/**
 * This class is using for datatable request paramaeter for transaction by batch
 * 
 * <AUTHOR>
 * @version 1.0
 *
 */
@Data
public class BatchNumberViewDataTableRequestBean {
	private DataTablesRequest dtRequest;
	@NotNull
	private String userId;
	@NotNull
	private String terminalId;
	@NotNull
	private String batchNumber;

	private String acquirerUserModule;
}
