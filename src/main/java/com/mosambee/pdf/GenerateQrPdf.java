package com.mosambee.pdf;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import com.mosambee.util.PDFUtil;
import com.mosambee.bean.PDFBean;
import com.mosambee.bean.PendingQrRequestBean;
import com.mosambee.util.TLVGenerator;
import lombok.extern.log4j.Log4j2;
import com.mosambee.util.PDFBeanBuilderUtil;
import com.mosambee.dao.StaticQrDao;
import com.mosambee.dao.impl.Transactions;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class GenerateQrPdf {

	private static final String SUCCESS = "success";

	@Autowired
	Transactions transactions;

	@Autowired
	PDFUtil pdfUtil;

	@Autowired
	TLVGenerator tlvGenerator;

	@Autowired
	private StaticQrDao dao;

	static Map<Integer, String> bqrMap = new HashMap<>();

	static {
		bqrMap.put(9, "125,125,29,32,90,35,5"); // YES-SR600-QR
		bqrMap.put(10, "90,90,52,30,95,31,3"); // YES-SR600-HAT-QR
		bqrMap.put(11, "280,280,17,19,150,20,10");// YES-TENT-QR
	}

	public PendingQrRequestBean generateQr(PendingQrRequestBean pendingQrRequestBean, long qrType) {
		boolean status = false;
		long userId = transactions.validateMidandTid(pendingQrRequestBean);

		log.info("Retrived user id for TID - " + pendingQrRequestBean.getTerminalId() + " is - " + userId);

		if (userId > 0) {
			JSONObject qrFields = transactions.getQrFields(userId, pendingQrRequestBean.getTerminalId());

			try {
				if (qrFields != null && qrFields.length() > 0
						&& SUCCESS.equalsIgnoreCase(qrFields.getString("result"))) {
					PDFBean pdfBean = PDFBeanBuilderUtil.getPdfBean(pendingQrRequestBean, qrFields);
					pdfBean.setQrType(String.valueOf(qrType));
					pdfBean.setUpiAddress(pendingQrRequestBean.getUpiAddress());

					Map<String, String> tlvMap = tlvGenerator.generateTlv(qrFields, pdfBean);

					log.info("Generated TLV [" + tlvMap.toString() + "] for TID -"
							+ pendingQrRequestBean.getTerminalId());

					if (SUCCESS.equalsIgnoreCase(tlvMap.get("message"))) {
						pdfBean.setTlvData(tlvMap.get("tlvData"));
						log.info(pdfBean);
						if (qrType == 0) {
							status = pdfUtil.generateDefault(pdfBean);
						} else if (qrType == 1) {
							status = pdfUtil.generateImage(pdfBean);
						} else if (qrType == 9 || qrType == 10 || qrType == 11) {
							status = pdfUtil.generateBQRUPIPdf(pdfBean, bqrMap.get((int) qrType).split(","));
						} else {
							status = pdfUtil.generatePdf(pdfBean);
						}

						if (status) {
							dao.updateStaticQrRequest(pendingQrRequestBean.getId());
							pendingQrRequestBean.setStatus(SUCCESS);
							pendingQrRequestBean.setVisaPan(qrFields.getString("tag02")); // Visa PAN
							pendingQrRequestBean.setMobileNo(qrFields.getString("merchantphone")); // Registered Mobile
																									// No
							pendingQrRequestBean.setEmailId(qrFields.getString("emailId")); // EMAIL ID
							pendingQrRequestBean.setMerchantAddress(qrFields.getString("address")); // Merchant Address
							pendingQrRequestBean.setMerchantName(qrFields.getString("tag59")); // Merchant Name
							pendingQrRequestBean.setMerchantCity(qrFields.getString("tag60")); // Merchant City
							pendingQrRequestBean.setMerchantPin(qrFields.getString("tag61")); // Merchant Postal
							pendingQrRequestBean.setMasterPan(qrFields.getString("tag04")); // Master Card PAN
							pendingQrRequestBean.setNpciPan(qrFields.getString("tag06")); // NPCI PAN

							if (qrFields.has("tag26")) {
								JSONObject tag26 = qrFields.getJSONObject("tag26");
								if (tag26.has("tag01")) {
									pendingQrRequestBean.setUpiAddress(tag26.getString("tag01"));
								}
							}
						}
					} else {
						pendingQrRequestBean.setStatus(tlvMap.get("message"));
					}
				}
			} catch (Exception e) {
				log.info(e.toString());
				pendingQrRequestBean
						.setStatus("Unable to generate QR for TID - " + pendingQrRequestBean.getTerminalId());
			}
		} else {
			log.info("Unable to retrive user ID for - " + pendingQrRequestBean.getTerminalId());
			pendingQrRequestBean.setStatus("Invalid MID or TID.");
		}
		return pendingQrRequestBean;
	}

	public PendingQrRequestBean generateAmazonQr(PendingQrRequestBean pendingQrRequestBean) {
		boolean status = false;
		try {
			PDFBean pdfBean = PDFBeanBuilderUtil.getUpiPdfBean(pendingQrRequestBean);
			String tlvData = tlvGenerator.generateAmazonTlv(pdfBean);
			pdfBean.setTlvData(tlvData);
			log.info(pdfBean);
			status = pdfUtil.generateAmazonImage(pdfBean);

			if (status && pdfBean.getStatus() == 0) {
				dao.updateAmazonStaticQrRequest(pendingQrRequestBean.getId());
				pendingQrRequestBean.setStatus(SUCCESS);
			}
		} catch (Exception e) {
			log.error(e);
			pendingQrRequestBean.setStatus("Unable to generate QR for VPA - " + pendingQrRequestBean.getUpiAddress());
		}
		return pendingQrRequestBean;
	}

	public PendingQrRequestBean generateUpiQr(PendingQrRequestBean pendingQrRequestBean, long qrType) {
		boolean status = false;
		long userId = transactions.validateMidandTid(pendingQrRequestBean);

		log.info("Retrived user id for TID - " + pendingQrRequestBean.getTerminalId() + " is - " + userId);

		if (userId > 0) {
			pendingQrRequestBean = transactions.getUpiQrFields(userId, pendingQrRequestBean);
			if (pendingQrRequestBean.getMsg().equals(SUCCESS)) {
				PDFBean pdfBean = PDFBeanBuilderUtil.getUpiPdfBean(pendingQrRequestBean);
				pdfBean.setQrType(String.valueOf(qrType));
				pdfBean.setUpiAddress(pendingQrRequestBean.getUpiAddress());
				log.info(pdfBean);

				String tlvData = "";
				if (pdfBean.getQrString() != null && !pdfBean.getQrString().isEmpty()) {
					tlvData = pdfBean.getQrString();
				} else {
					tlvData = tlvGenerator.generateUpiTlv(pdfBean);
				}

				log.info(tlvData);
				pdfBean.setTlvData(tlvData);
				if (qrType == 0) {
					status = pdfUtil.generateUpiImage(pdfBean);
				} else if (qrType == 3 || qrType == 5) {
					status = pdfUtil.generateQr(pdfBean, pendingQrRequestBean.getQrTxnType());
				} else {
					status = pdfUtil.generatePaperUpiPdf(pdfBean);
				}

				if (status) {
					dao.updateStaticQrRequest(pendingQrRequestBean.getId());
					pendingQrRequestBean.setStatus(SUCCESS);
					pendingQrRequestBean.setMobileNo(pendingQrRequestBean.getMobileNo());
					pendingQrRequestBean.setEmailId(pendingQrRequestBean.getEmailId()); // EMAIL ID
					pendingQrRequestBean.setMerchantAddress(pendingQrRequestBean.getMerchantAddress()); // Address
					pendingQrRequestBean.setMerchantName(pendingQrRequestBean.getMerchantName()); // Merchant Name
					pendingQrRequestBean.setMerchantCity(pendingQrRequestBean.getMerchantCity()); // Merchant City
					pendingQrRequestBean.setMerchantPin(pendingQrRequestBean.getMerchantPin()); // Merchant Postal
					pendingQrRequestBean.setUpiAddress(pendingQrRequestBean.getUpiAddress());
				}
			}
		} else {
			log.info("Unable to retrive user ID for - " + pendingQrRequestBean.getTerminalId());
			pendingQrRequestBean.setStatus("Invalid MID or TID.");
		}
		return pendingQrRequestBean;
	}

	public static String getDateForQrFileName() {
		String pattern = "ddMMyy";
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
		return simpleDateFormat.format(new Date());
	}
}