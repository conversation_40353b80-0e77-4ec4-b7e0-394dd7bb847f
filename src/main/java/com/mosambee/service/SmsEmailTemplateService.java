package com.mosambee.service;

import java.util.List;
import java.util.Map;

import com.mosambee.bean.AcquirerBean;
import com.mosambee.bean.EditSmsEmailTemplateBean;
import com.mosambee.bean.SmsEmailTemplateBean;
import com.mosambee.bean.SmsEmailTemplateRequestBean;
import com.mosambee.bean.datatables.DataTablesResponse;

public interface SmsEmailTemplateService {
	List<AcquirerBean> getListOfAcquirer();

	DataTablesResponse<SmsEmailTemplateBean> getTemplateList(SmsEmailTemplateRequestBean dtRequest);

	EditSmsEmailTemplateBean getTemplateDetails(SmsEmailTemplateBean editBeanData);

	String updateTemplateDetails(EditSmsEmailTemplateBean updateBeanData, String id);

	Map<Integer, String> getTxnTypeMap();

	EditSmsEmailTemplateBean getViewTemplateData(SmsEmailTemplateBean editBeanData);
}
