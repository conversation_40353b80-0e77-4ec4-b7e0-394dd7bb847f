package com.mosambee.service;

import java.util.List;

import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import com.mosambee.service.impl.AmexUserUploadServiceImpl;


/**
 * This class provides specification for {@link AmexUserUploadServiceImpl}
 * 
 * <AUTHOR>
 * @version 1.0
 *
 */
public interface AmexUserUploadService {
	Resource getAmexUploadFormat();
	Resource processAmexUserUpload(MultipartFile file);
	List<String> getAmexMatCode();
}
