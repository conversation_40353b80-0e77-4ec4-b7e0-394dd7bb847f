package com.mosambee.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mosambee.bean.ApiPasswordBean;
import com.mosambee.bean.ApiPasswordDataBean;
import com.mosambee.bean.ResponseBean;
import com.mosambee.bean.UpdateAPIPaswordConfigRequestFromList;
import com.mosambee.bean.datatables.DataTablesRequest;
import com.mosambee.bean.datatables.DataTablesResponse;
import com.mosambee.constants.ColumnNames;
import com.mosambee.constants.ViewLayer;
import com.mosambee.dao.ApiPasswordDao;
import com.mosambee.service.ApiPasswordService;
import com.mosambee.transformer.ApiPasswordTransformer;
import com.mosambee.validator.ApiPasswordValidator;

import lombok.extern.log4j.Log4j2;

/**
 * ApiPPasswordServiceImpl class implementing {@link ProductService}
 * specification.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 16-March-2020
 */
@Log4j2
@Service(value = "apiPPasswordService")
public class ApiPPasswordServiceImpl implements ApiPasswordService{
	
	@Autowired
	private ApiPasswordTransformer transformer;
	
	@Autowired
	private ApiPasswordDao dao;
	
	@Autowired
	private ApiPasswordValidator validator;

	private static final String RECORD_ID = "recordId";
	
	/**
	 * getActiveApiPassword(...) is responsible for calling the transformer for
	 * transforming the incoming input fields in the data-tables request & then
	 * calling the DAO to get those values.
	 * 
	 * @param dtRequest {@link DataTablesRequest}
	 * @return DataTablesResponse of ApiPasswordBean
	 */
	public DataTablesResponse<ApiPasswordBean> getActiveApiPassword(ApiPasswordDataBean dtRequest) {

		// GET THE COLUMN NAME FROM COLUMN INDEX
		int orderingColumnIndex = dtRequest.getDataTable().getOrder().get(0).getColumn();
		String orderingColumnName = getOrderingColumnName(orderingColumnIndex);
		log.info("orderingColumnIndex: {}, orderingColumnName: {}", orderingColumnIndex, orderingColumnName);

		// TRANSFORM THE INPUT FIELDS AND GET & RETURN THE LIST OF ACTIVE API PASSWORD
		Map<String, String> searchMap = transformer.transformApiPasswordDataTableRequest(dtRequest);
		log.info("size of searchMap: {}", searchMap.size());

		return dao.getActiveApiPassword(dtRequest, orderingColumnName, searchMap);
	}

	/**
	 * getOrderingColumnName(...) method is responsible for returning
	 * orderingColumnName when it is provided with orderingColumnIndex.
	 * 
	 * @param orderingColumnIndex
	 * @return String orderingColumnName
	 */
	private String getOrderingColumnName(int orderingColumnIndex) {

		String orderingColumnName = "";

		switch (orderingColumnIndex) {
		case 0:
			orderingColumnName = ColumnNames.API_PASSWORD_ID.get();
			break;
		case 1:
			orderingColumnName = ColumnNames.API_PASSWORD_BUSINESS_NAME.get();
			break;
		case 2:
			orderingColumnName = ColumnNames.API_PASSWORD_MERCHANT_CODE.get();
			break;
		case 3:
			orderingColumnName = ColumnNames.API_PASSWORD.get();
			break;

		default:
			orderingColumnName = ColumnNames.API_PASSWORD_ID.get();
			break;
		}

		return orderingColumnName;
	}
	
	/**
	 * This method is basically calling {@link #updateApiPassword()} of
	 * {@link APIPasswordDao} if it return "True" then set
	 * {@link ResponseBean} "operationStatus" to 200 and "action" to "update action"
	 * otherwise set "operationStatus" to 400.
	 */
	@Override
	public void updateApiPassword(UpdateAPIPaswordConfigRequestFromList updateAPIPaswordConfigRequestFromList,
			ApiPasswordBean createAPIGroup, ResponseBean responseBean) {
		responseBean.setData(new HashMap<>());
		responseBean.getData().put(RECORD_ID, createAPIGroup.getId()); // This id will render on jsp page which will be
																		// used for update APIPassword request.
		boolean status = dao.updateApiPassword(updateAPIPaswordConfigRequestFromList, createAPIGroup,
				responseBean); // Fetch APIPassword details by passing
								// updateAPIPasword.
		if (status) {
			responseBean.setOperationStatus(200);
			responseBean.setAction(ViewLayer.API_PASSWORD_UPDATE_ACTION.get());
			responseBean.getData().put("breadCrumb", true);
			responseBean.setUpdateRequest(true);
		} else {
			responseBean.setOperationStatus(400);
		}

	}
	
	/**
	 * This method is used to call validation methods on {@link ApiPasswordBean}.
	 * Also this method checks that bean is validated or not if bean is validated it
	 * allows to update record in database by calling
	 * {@link #updateAPIGroup(ApiPasswordBean, responseBean, id)} of
	 * {@link APIPasswordDao}. If bean failed in validation then it set
	 * {@link ResponseBean} class field "operationStatus" to 400, If data is
	 * successfully inserted then set "updateRequest" to "True" and
	 * "operationStatus" to 200 otherwise set "operationStatus" to 400 and
	 * "updateRequest" to "False".
	 * 
	 * @param responseBean   : Expect bean of "{@link ResponseBean}" which is used
	 *                       to set operation status message. We can use this to to
	 *                       render data related to status message to jsp.
	 * @param ApiPasswordBean : Expect bean of "{@link ApiPasswordBean}" from which
	 *                       data should be store.
	 * @return void
	 */
	@Override
	public void processUpdate(ApiPasswordBean updateApi, ResponseBean responseBean) {

		validator.validateApiPassword(updateApi); // This call is used to validate
																				// "createAPIGroup" bean.
		if (updateApi.isValidate()) // Check that bean validation successful or not.
		{
			responseBean.setUpdateRequest(true);
			responseBean.setAction(ViewLayer.API_PASSWORD_UPDATE.get()); // Set form action to update record.
			boolean status = dao.updateApi(updateApi, responseBean);
			log.info("falg :{}", updateApi.getFlag());

			responseBean.setData(new HashMap<>());
			responseBean.getData().put(RECORD_ID, updateApi.getId());
			if (status) // Checks that Update record operation successful or not.
			{
				responseBean.setOperationStatus(200);
			} else {
				responseBean.setOperationStatus(400);
			}
		} else {
			responseBean.setMsg("Sorry, Data is not valid.");
			responseBean.setOperationStatus(400);
		}
		log.info("returning from processUpdate {} ", updateApi.isValidate());
	}
	
}
