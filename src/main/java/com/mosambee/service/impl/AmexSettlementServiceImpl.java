package com.mosambee.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mosambee.bean.AmexSettlementBean;
import com.mosambee.dao.AmexSettlementDao;
import com.mosambee.service.AmexSettlementService;

/**
 * This is responsible to calling dao to perform amex settement
 * 
 * <AUTHOR>
 *
 */
@Service("amexSettlementService")
public class AmexSettlementServiceImpl implements AmexSettlementService {
	@Autowired
	private AmexSettlementDao amexSettlementDao;

	@Override
	public String processAmexSettlement(AmexSettlementBean bean) {

		return amexSettlementDao.processAmexSettlement(bean);
	}

}
