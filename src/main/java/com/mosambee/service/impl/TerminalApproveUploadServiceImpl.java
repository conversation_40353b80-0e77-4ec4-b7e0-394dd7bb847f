package com.mosambee.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.mosambee.bean.CustomUser;
import com.mosambee.bean.MappingAcqOnboardingDataBean;
import com.mosambee.bean.TerminalApproveBean;
import com.mosambee.constants.BulkUploadCategory;
import com.mosambee.constants.BulkUploadFileLocation;
import com.mosambee.constants.BulkUploadMessages;
import com.mosambee.dao.TerminalApproveUploadDao;
import com.mosambee.properties.ExcelHeaderProperties;
import com.mosambee.service.ExcelService;
import com.mosambee.service.MerchantService;
import com.mosambee.service.TerminalApproveUploadService;
import com.mosambee.validator.TerminalApproveUploadValidator;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Service("terminalApproveUploadService")
public class TerminalApproveUploadServiceImpl implements TerminalApproveUploadService {

	private static final DataFormatter dataFormatter = new DataFormatter();

	@Autowired
	private ExcelService excelService;

	@Autowired
	private ExcelHeaderProperties excelHeaderProperties;

	@Autowired
	private TerminalApproveUploadValidator validator;

	@Autowired
	private TerminalApproveUploadDao terminalApproveUploadDao;

	@Autowired
	private MerchantService merchantService;

	public static final String LOGON_SUCCESSFUL = "Logon Successful";

	@Override
	public Resource getTerminalApproveUploadFormat() {
		ClassPathResource resource = null;
		resource = new ClassPathResource(BulkUploadFileLocation.TERMINAL_APPROVE_UPLOAD.get());
		return resource.exists() ? resource : null;
	}

	@Override
	public Resource processTerminalApproveUpload(MultipartFile file) {
		Workbook workbook = excelService.getWorkbookFromMultipartFile(file);
		List<TerminalApproveBean> terminalApproveUploadBeanList = new ArrayList<>();
		List<TerminalApproveBean> successRecordList = new ArrayList<>();
		List<TerminalApproveBean> failedRecordList = new ArrayList<>();
		log.info("Number of Rows in Excel: {}", workbook.getSheetAt(0).getPhysicalNumberOfRows() - 1);
		int numberOfApproves = getNumberOfApprovesFromExcel(workbook);
		log.info("Number of Approve Rows found: {}", numberOfApproves);
		log.info("Number of Reject Rows: {}", workbook.getSheetAt(0).getPhysicalNumberOfRows() - numberOfApproves - 1);

		if (numberOfApproves <= 10000) {
			// VALIDATE THE HEADER & VALIDATE SECURITY ISSUES
			if (validateExcelFile(file, workbook) && validateSecurityIssues(file, workbook)) {
				// PARSE DATA FROM WORKBOOK
				List<TerminalApproveBean> terminalApproveBeanList = parseTerminalApproveUploadFields(workbook);

				// VALIDATE THE PARSED DATA FROM WORKBOOK INTO SUCCESS & FAILED RECORD LIST
				validateAndTransformParsedDataIntoLists(terminalApproveBeanList, successRecordList, failedRecordList);

				// PERSISTS TERMINAL USERS UPLAOD FIELDS IN DATABASE
				persistsTerminalApproveUpload(successRecordList);

				// ADD ALL SUCCESS AND FAILED RECORDS IN A COMMON LIST
				terminalApproveUploadBeanList.addAll(failedRecordList);
				terminalApproveUploadBeanList.addAll(successRecordList);

				// GET THE EXCEL WITH RESPONSE
				Workbook responseWorkbook = writeTerminalApproveUploadBeanListToExcel(terminalApproveUploadBeanList);

				Resource resource = excelService.getResourceFromWorkbook(responseWorkbook);

				try {
					responseWorkbook.close();
				} catch (IOException e) {

					log.info("error occured {}", e);
				}

				return resource;

			} else {
				log.error("Excel file is not valid");
			}
		} else {
			log.error("Excel file has more than 5000 Approve Rows");
		}
		try {
			workbook.close();
		} catch (IOException e) {

			log.info("error occured {}", e);
		}
		return null;
	}

	private int getNumberOfApprovesFromExcel(Workbook workbook) {

		Iterator<Row> rowIterator = workbook.getSheetAt(0).iterator();

		// SKIPING THE HEADER ROW
		rowIterator.next();
		int approveCount = 0;

		while (rowIterator.hasNext()) {

			Row row = rowIterator.next();
			TerminalApproveBean terminalApproveBean = parseRow(row);
			
			if (terminalApproveBean.getApproveReject().equalsIgnoreCase("A")) {
				approveCount++;
			}
		}
		return approveCount;
	}

	private boolean validateExcelFile(MultipartFile file, Workbook workbook) {
		return excelService.validateExtension(file)
				&& excelService.validateHeader(workbook, BulkUploadCategory.TERMINAL_APPROVE_UPLOAD);
	}

	private boolean validateSecurityIssues(MultipartFile file, Workbook workbook) {
		return excelService.checkForEmbeddedObjects(workbook) && excelService.checkForMacrosAndVbScript(file);
	}

	private List<TerminalApproveBean> parseTerminalApproveUploadFields(Workbook workbook) {
		Iterator<Row> rowIterator = workbook.getSheetAt(0).iterator();
		List<TerminalApproveBean> terminalApproveBeanList = new ArrayList<>();
		// SKIPING THE HEADER ROW
		rowIterator.next();

		while (rowIterator.hasNext()) {
			Row row = rowIterator.next();
			TerminalApproveBean terminalApproveBean = parseRow(row);
			terminalApproveBeanList.add(terminalApproveBean);
		}
		return terminalApproveBeanList;
	}

	private TerminalApproveBean parseRow(Row row) {
		TerminalApproveBean terminalApproveBean = new TerminalApproveBean();

		// SUBTRACT 1 FROM THE SIZE AS IT HAS STATUS HEADER.
		int sizeOfExcelHeader = excelHeaderProperties.getTerminalApproveUploadHeaders().size() - 1;
	
		for (int i = 0; i < sizeOfExcelHeader; ++i) {

			Cell cell = row.getCell(i);

			switch (i) {
			case 0:
				terminalApproveBean.setUsername(dataFormatter.formatCellValue(cell));
				break;
			case 1:
				terminalApproveBean.setTid(dataFormatter.formatCellValue(cell));
				break;
			case 2:
				terminalApproveBean.setMatCode(dataFormatter.formatCellValue(cell));
				break;
			case 3:
				terminalApproveBean.setApproveReject(dataFormatter.formatCellValue(cell));
				break;
			case 4:
				terminalApproveBean.setRejectReason(dataFormatter.formatCellValue(cell));
				break;
			default:
				String defaultCellValue = dataFormatter.formatCellValue(cell);
				log.info("Getting into the default case: {}", defaultCellValue);
				break;
			}
		}
		return terminalApproveBean;
	}

	private void validateAndTransformParsedDataIntoLists(List<TerminalApproveBean> terminalApproveBeanList,
			List<TerminalApproveBean> successRecordList, List<TerminalApproveBean> failedRecordList) {

		// LOOP OVER THE LIST OF MID
		for (TerminalApproveBean terminalApproveBean : terminalApproveBeanList) {

			// VALIDATE THE BEAN
			TerminalApproveBean terminalApproveUploadBean = validator.validateTerminalApproveBean(terminalApproveBean);

			// PUT THEM IN RESPECTIVE LIST.
			if (terminalApproveUploadBean.getStatus().equals("")) {
				successRecordList.add(terminalApproveUploadBean);
			} else {
				failedRecordList.add(terminalApproveUploadBean);
			}
		}
	}

	private void persistsTerminalApproveUpload(List<TerminalApproveBean> successRecordList) {
		String response = "";
		String response1 = "";
		// INSERT INTO DB
		for (TerminalApproveBean terminalApproveUploadBean : successRecordList) {

				TerminalApproveBean terminalApproveBean = terminalApproveUploadDao.validatePendingTerminalUser(terminalApproveUploadBean);
				
				if (terminalApproveBean.getResponse().equalsIgnoreCase("0")) {
					String userId;
					CustomUser customUser = (CustomUser) SecurityContextHolder.getContext().getAuthentication()
							.getPrincipal();
					Map<String, String> myMap = customUser.getMyMap();
					// Store logged in use into hash map.
					userId = myMap.get("id");
					if (terminalApproveBean.getApproveReject().equalsIgnoreCase("A")) {
						terminalApproveUploadDao
								.approveMultipleUser(terminalApproveUploadBean, userId);
						response=terminalApproveUploadBean.getResponse();
					} else if (terminalApproveBean.getApproveReject().equalsIgnoreCase("R")) {
						response = terminalApproveUploadDao.rejectMultipleUser(terminalApproveUploadBean, userId);
						if (!response.equalsIgnoreCase("0")) {
							response = "2";
						}

					}
				} else {
					response = "1";
				}

			updateStatus(response, terminalApproveUploadBean);
			
		}
	}

	private void updateStatus(String response, TerminalApproveBean terminalApproveUploadBean) {
		if (null == response) {
			terminalApproveUploadBean.setStatus(BulkUploadMessages.EXCEPTION_OCCURED_WHILE_INSERTION.get());
		} else if (response.equalsIgnoreCase("0")) {
			terminalApproveUploadBean.setStatus(BulkUploadMessages.SUCCESS.get());
		} else if (response.equalsIgnoreCase("1")) {
			terminalApproveUploadBean
					.setStatus(BulkUploadMessages.INVALIDTERMINALUSER.get().concat("/Merchant is not active"));
		} else if (response.equalsIgnoreCase("2")) {
			terminalApproveUploadBean.setStatus(BulkUploadMessages.REJECT_ERROR.get());
		} else if (response.equalsIgnoreCase("3")) {
			terminalApproveUploadBean.setStatus(BulkUploadMessages.APPROVEERROR.get());
		} else if (response.equalsIgnoreCase("4")) {
			terminalApproveUploadBean.setStatus(BulkUploadMessages.TSS.get());
		}
	}

	private Workbook writeTerminalApproveUploadBeanListToExcel(
			List<TerminalApproveBean> terminalApproveUploadBeanList) {
		// CREATE THE WORKBOOK AND WRITE THE HEADER WITH CUSTOM STYLING
		Workbook workbook = excelService.createHeaderRow(BulkUploadCategory.TERMINAL_APPROVE_UPLOAD);
		Sheet sheet = workbook.getSheetAt(0);

		// WRITE THE DATA TO THE EXCEL FILE
		int rowNum = 1;
		for (TerminalApproveBean terminalApproveBean : terminalApproveUploadBeanList) {
			Row row = sheet.createRow(rowNum++);
			row.createCell(0).setCellValue(terminalApproveBean.getUsername());
			row.createCell(1).setCellValue(terminalApproveBean.getTid());
			row.createCell(2).setCellValue(terminalApproveBean.getMatCode());
			row.createCell(3).setCellValue(terminalApproveBean.getApproveReject());
			row.createCell(4).setCellValue(terminalApproveBean.getRejectReason());
			row.createCell(5).setCellValue(terminalApproveBean.getStatus());

		}
		excelService.autoSizeExcel(workbook);
		return workbook;
	}
}