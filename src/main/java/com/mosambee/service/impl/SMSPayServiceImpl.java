package com.mosambee.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.mosambee.bean.SMSPayBean;
import com.mosambee.bean.SMSPayUploadBean;
import com.mosambee.constants.BulkUploadCategory;
import com.mosambee.constants.BulkUploadFileLocation;
import com.mosambee.constants.BulkUploadMessages;
import com.mosambee.dao.SMSPayDao;
import com.mosambee.properties.ExcelHeaderProperties;
import com.mosambee.service.ExcelService;
import com.mosambee.service.SMSPayService;
import com.mosambee.validator.SMSPayUploadValidator;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Service("sMSPayService")
public class SMSPayServiceImpl implements SMSPayService {

	@Autowired
	private SMSPayDao dao;

	@Autowired
	private ExcelService excelService;

	@Autowired
	private ExcelHeaderProperties excelHeaderProperties;

	@Autowired
	private SMSPayUploadValidator sMSPayUploadValidator;

	private static final DataFormatter dataFormatter = new DataFormatter();

	/**
	 * getBulkUploadFormat() is responsible for converting the settlement bulk
	 * upload files present in {@link BulkUploadFileLocation} to {@link Resource}
	 */
	@Override
	public Resource getBulkUploadFormat() {
		ClassPathResource resource = null;
		resource = new ClassPathResource(BulkUploadFileLocation.SMS_PAY_UPLOAD.get());
		return resource.exists() ? resource : null;
	}

	@Override
	public Resource processBulkUploadExcel(MultipartFile file) {
		Workbook workbook = excelService.getWorkbookFromMultipartFile(file);

		List<SMSPayUploadBean> bulkUploadBeanList = new ArrayList<>();
		List<SMSPayUploadBean> successRecordList = new ArrayList<>();
		List<SMSPayUploadBean> failedRecordList = new ArrayList<>();
		// VALIDATE THE HEADER & VALIDATE SECURITY ISSUES
		if (validateExcelFile(file, workbook) && validateSecurityIssues(file, workbook)) {

			// PARSE DATA FROM WORKBOOK
			List<SMSPayBean> beanList = parseBulkUploadFields(workbook);

			// VALIDATE THE PARSED DATA FROM WORKBOOK INTO SUCCESS & FAILED RECORD LIST
			validateAndTransformParsedDataIntoLists(beanList, successRecordList, failedRecordList);

			persist(successRecordList);

			// ADD ALL SUCCESS AND FAILED RECORDS IN A COMMON LIST
			bulkUploadBeanList.addAll(failedRecordList);
			bulkUploadBeanList.addAll(successRecordList);

			// GET THE EXCEL WITH RESPONSE
			Workbook responseWorkbook = writeBulkUploadBeanListToExcel(bulkUploadBeanList);

			Resource resource = excelService.getResourceFromWorkbook(responseWorkbook);

			try {
				responseWorkbook.close();
			} catch (IOException e) {

				log.info("error occured {}", e);
			}

			return resource;
		} else {
			log.error("Excel file is not valid");
		}
		return null;
	}

	/**
	 * persist(...) is responsible for getting the response from
	 * TempMidTidUploadBean
	 * 
	 * @param successRecordList
	 * @return void
	 */
	private void persist(List<SMSPayUploadBean> successRecordList) {
		// INSERT INTO THE DB
		for (SMSPayUploadBean sMSPayUploadBean : successRecordList) {
			if (sMSPayUploadBean.getKeyValue() == null || sMSPayUploadBean.getKeyValue().isEmpty()
					|| sMSPayUploadBean.getKeyValue().equalsIgnoreCase("N")) {
				String response = dao.upload(sMSPayUploadBean);
				// PROCESS THE RESPONSE
				updateSmsPayBean(response, sMSPayUploadBean);
			} else {
				Map<String, String> map = new HashMap<>();
				boolean value = dao.checkForMMUTID(sMSPayUploadBean.getBaseMid(), sMSPayUploadBean.getBaseTid(), map);
				if (value) {
					log.info("Map====={}", map);
					boolean response = dao.checkForMDATID(sMSPayUploadBean.getBaseMid(), map.get("posId"));
					if (response) {
						dao.updateTerminalIdRef1(map.get("mmutId"), sMSPayUploadBean.getKey());
						sMSPayUploadBean.setStatus("Success");
					} else {
						sMSPayUploadBean.setStatus("smspay not cofigure for the mid");
					}
				} else {
					sMSPayUploadBean.setStatus("Tid not exists");
				}
			}

		}
	}

	/**
	 * updateSmsPayBean is responsible for checking the response coming from db and
	 * if response is null,that means exception has occurred on DB side update
	 * status according to response
	 */
	private void updateSmsPayBean(String response, SMSPayUploadBean sMSPayUploadBean) {
		if (null == response) {
			sMSPayUploadBean.setStatus(BulkUploadMessages.EXCEPTION_OCCURED_WHILE_INSERTION.get());
		} else if (response.equals("0")) {
			sMSPayUploadBean.setStatus(BulkUploadMessages.SUCCESS.get());
		} else if (response.equals("1")) {
			sMSPayUploadBean.setStatus("Base-MID/Base TID not valid or Terminal not active.");
		} else if (response.equals("2")) {
			sMSPayUploadBean.setStatus("Merchant Not Active.");
		} else if (response.equals("3")) {
			sMSPayUploadBean.setStatus("MAT active flag is Not Configured.");
		} else if (response.equals("4")) {
			sMSPayUploadBean.setStatus("MID is Not Configured for the Provided Acquirer.");
		} else if (response.equals("5")) {
			sMSPayUploadBean.setStatus(
					sMSPayUploadBean.getAltIdValue() != null && sMSPayUploadBean.getAltIdValue().equalsIgnoreCase("Y")
							? "SMS Pay Already configured (ALT ID KEY REQUESTED)"
							: "SMS Pay Already configured.");
		} else if (response.equals("6")) {
			sMSPayUploadBean.setStatus("PayByLink Not Enabled For TID.");
		} else if (response.equals("7")) {
			sMSPayUploadBean.setStatus("Invalid MatCode for this MID.");
		} else if (response.equals("8")) {
			sMSPayUploadBean.setStatus("More than 1 base terminal exists.");
		} else {
			sMSPayUploadBean.setStatus(BulkUploadMessages.EXCEPTION_OCCURED_WHILE_INSERTION.get());
		}
	}

	/**
	 * Performs validation for excel extension and validates the excel header.
	 * 
	 * @param file     MultipartFile
	 * @param workbook Workbook
	 * @return boolean
	 */
	private boolean validateExcelFile(MultipartFile file, Workbook workbook) {
		return excelService.validateExtension(file)
				&& excelService.validateHeader(workbook, BulkUploadCategory.SMS_PAY_UPLOAD);
	}

	/**
	 * Performs validation for security issues in excel file. Mainly we are check
	 * for embedded object, macros and VB Scripts.
	 *
	 * @param file     MultipartFile
	 * @param workbook Workbook
	 * @return boolean
	 */
	private boolean validateSecurityIssues(MultipartFile file, Workbook workbook) {
		return excelService.checkForEmbeddedObjects(workbook) && excelService.checkForMacrosAndVbScript(file);
	}

	/**
	 * parseBulkUploadFields(...) is responsible to parsing the workbook. Here we
	 * are parsing the sheet present at 0th index and then iterating the rows and
	 * cells inside them to extract the values.
	 * 
	 * @param workbook {@link Workbook}
	 * @return {@link List} of {@link SMSPayBean}
	 */
	private List<SMSPayBean> parseBulkUploadFields(Workbook workbook) {
		Iterator<Row> rowIterator = workbook.getSheetAt(0).iterator();
		List<SMSPayBean> beanList = new ArrayList<>();

		// SKIPING THE HEADER ROW
		rowIterator.next();

		while (rowIterator.hasNext()) {
			Row row = rowIterator.next();
			SMSPayBean bean = parseRow(row);
			beanList.add(bean);
		}
		return beanList;
	}

	/**
	 * {@link #parseRow(Row)} method is responsible for parsing the row and storing
	 * the parsed value in the object of {@link SMSPayBean}
	 * 
	 * @param row
	 * @return {@link SMSPayBean}
	 */
	private SMSPayBean parseRow(Row row) {
		SMSPayBean smsPayBean = new SMSPayBean();
		// SUBTRACT 1 FROM THE SIZE AS IT HAS STATUS HEADER.
		int sizeOfExcelHeader = excelHeaderProperties.getSmsPayBulkUploadHeaders().size() - 1;
		log.info("sizeOfExcelHeader---------{}", sizeOfExcelHeader);
		for (int i = 0; i < sizeOfExcelHeader; ++i) {
			Cell cell = row.getCell(i);
			switch (i) {
			case 0:
				smsPayBean.setSmsPayMatCode(dataFormatter.formatCellValue(cell));
				break;
			case 1:
				smsPayBean.setBaseMid(dataFormatter.formatCellValue(cell));
				break;
			case 2:
				smsPayBean.setBaseTid(dataFormatter.formatCellValue(cell));
				break;
			case 3:
				smsPayBean.setRetailerRegion(dataFormatter.formatCellValue(cell));
				break;
			case 4:
				smsPayBean.setRetailerGroup(dataFormatter.formatCellValue(cell));
				break;
			case 5:
				smsPayBean.setCityCode(dataFormatter.formatCellValue(cell));
				break;
			case 6:
				smsPayBean.setStateCode(dataFormatter.formatCellValue(cell));
				break;
			case 7:
				smsPayBean.setCountryCode(dataFormatter.formatCellValue(cell));
				break;
			case 8:
				smsPayBean.setKeyValue(dataFormatter.formatCellValue(cell));
				break;
			case 9:
				smsPayBean.setKey(dataFormatter.formatCellValue(cell));
				break;
			case 10:
				smsPayBean.setAltIdValue(dataFormatter.formatCellValue(cell));
				break;
			default:
				String defaultCellValue = dataFormatter.formatCellValue(cell);
				log.info("Getting into the default case: {}", defaultCellValue);
				break;
			}
		}
		return smsPayBean;
	}

	/**
	 * <strong>validateAndTransformParsedDataIntoLists(...)</strong> is responsible
	 * for iterating over the list of smsPay upload and then putting them into two
	 * separate list, on the basis of their success and failed validation status.
	 * <br>
	 * <br>
	 * 
	 * Here we are using two lists, i.e. successRecordList & failedRecordList for
	 * success and failed records. <br>
	 * 
	 * @param beanList          {@link List} of all the SMSPayBean details.
	 * @param successRecordList {@link List} of all the success records.
	 * @param failedRecordList  {@link List} of all the failed records.
	 * @return void
	 */
	private void validateAndTransformParsedDataIntoLists(List<SMSPayBean> beanList,
			List<SMSPayUploadBean> successRecordList, List<SMSPayUploadBean> failedRecordList) {
		// LOOP OVER THE LIST
		for (SMSPayBean Bean : beanList) {

			// VALIDATE THE BEAN
			SMSPayUploadBean uploadBean = sMSPayUploadValidator.validateSmsPayBean(Bean);

			// PUT THEM IN RESPECTIVE LIST.
			if (uploadBean.getStatus().equals("")) {
				successRecordList.add(uploadBean);
			} else {
				failedRecordList.add(uploadBean);
			}
		}

	}

	/**
	 * {@link #writeBulkUploadBeanListToExcel(List)} is responsible for for creating
	 * the excel with the response after processing the smsPay upload excel file.
	 * 
	 * @param bulkUploadBeanList
	 * @return {@link Workbook}
	 */
	private Workbook writeBulkUploadBeanListToExcel(List<SMSPayUploadBean> bulkUploadBeanList) {
		// CREATE THE WORKBOOK AND WRITE THE HEADER WITH CUSTOM STYLING
		Workbook workbook = excelService.createHeaderRow(BulkUploadCategory.SMS_PAY_UPLOAD);
		Sheet sheet = workbook.getSheetAt(0);
		// WRITE THE DATA TO THE EXCEL FILE
		int rowNum = 1;
		for (SMSPayUploadBean sMSPayUploadBean : bulkUploadBeanList) {
			Row row = sheet.createRow(rowNum++);
			row.createCell(0).setCellValue(sMSPayUploadBean.getSmsPayMatCode());
			row.createCell(1).setCellValue(sMSPayUploadBean.getBaseMid());
			row.createCell(2).setCellValue(sMSPayUploadBean.getBaseTid());
			row.createCell(3).setCellValue(sMSPayUploadBean.getRetailerRegion());
			row.createCell(4).setCellValue(sMSPayUploadBean.getRetailerGroup());
			row.createCell(5).setCellValue(sMSPayUploadBean.getCityCode());
			row.createCell(6).setCellValue(sMSPayUploadBean.getStateCode());
			row.createCell(7).setCellValue(sMSPayUploadBean.getCountryCode());
			row.createCell(8).setCellValue(sMSPayUploadBean.getKeyValue());
			row.createCell(9).setCellValue(sMSPayUploadBean.getKey());
			row.createCell(10).setCellValue(sMSPayUploadBean.getAltIdValue());
			row.createCell(11).setCellValue(sMSPayUploadBean.getStatus());
		}
		excelService.autoSizeExcel(workbook);
		return workbook;
	}
}