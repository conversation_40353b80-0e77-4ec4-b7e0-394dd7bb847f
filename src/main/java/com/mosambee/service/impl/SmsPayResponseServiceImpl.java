package com.mosambee.service.impl;

import java.io.IOException;
import java.util.List;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import com.mosambee.bean.SmsPayResponseBean;
import com.mosambee.bean.SmsPayResponseExcelBean;
import com.mosambee.constants.BulkUploadCategory;
import com.mosambee.dao.SmsPayResponseDao;
import com.mosambee.service.ExcelService;
import com.mosambee.service.SmsPayResponseService;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Service("smsPayResponseService")
public class SmsPayResponseServiceImpl implements SmsPayResponseService {

	@Autowired
	private SmsPayResponseDao dao;

	@Autowired
	private ExcelService excelService;

	@Override
	public List<String> getModuleName() {
		return dao.getModuleName();
	}

	@Override
	public List<SmsPayResponseExcelBean> getDataToDownload(SmsPayResponseBean bean) {
		return dao.getDataToDownload(bean);
	}

	@Override
	public Resource downloadSMSPAYResponse(List<SmsPayResponseExcelBean> list) {
		Resource res = null;

		res = process(list);
		return res;
	}

	private Resource process(List<SmsPayResponseExcelBean> list) { // GET THE EXCEL WITH RESPONSE
		Workbook responseWorkbook = writeListToExcel(list);

		Resource resource = excelService.getResourceFromWorkbook(responseWorkbook);

		try {
			responseWorkbook.close();
		} catch (IOException e) {

			log.info("error occured {}", e);
		}

		return resource;
	}

	private Workbook writeListToExcel(List<SmsPayResponseExcelBean> responseBean) { // CREATE THE WORKBOOK AND WRITE THE
		// HEADER WITH CUSTOM STYLING
		Workbook workbook = excelService.createHeaderRow(BulkUploadCategory.SMSPAY_LIST_DOWNLOAD);

		Sheet sheet = workbook.getSheetAt(0);

// WRITE THE DATA TO THE EXCEL FILE
		int rowNum = 1;
		for (SmsPayResponseExcelBean bean : responseBean) {

			Row row = sheet.createRow(rowNum++);
			row.createCell(0).setCellValue(bean.getRefid1());
			row.createCell(1).setCellValue(bean.getRefid2());
			row.createCell(2).setCellValue(bean.getRefid3());
			row.createCell(3).setCellValue(bean.getAcquirer());
			row.createCell(4).setCellValue(bean.getRequestJson());
			row.createCell(5).setCellValue(bean.getResponseJson());
			row.createCell(6).setCellValue(bean.getStatus());
			row.createCell(7).setCellValue(bean.getCreatedDate());
			row.createCell(8).setCellValue(bean.getUpdatedDate());
			row.createCell(9).setCellValue(bean.getRefid4());
			row.createCell(10).setCellValue(bean.getRefid5());
			row.createCell(11).setCellValue(bean.getRefid6());

		}
		excelService.autoSizeExcel(workbook);

		String sheetName = "API Response";
		int sheetCount = workbook.getNumberOfSheets();
		workbook.setSheetName(sheetCount - 1, sheetName);

		return workbook;
	}

}
