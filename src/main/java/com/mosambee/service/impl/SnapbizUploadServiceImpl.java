package com.mosambee.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.mosambee.bean.CustomUser;
import com.mosambee.bean.NiUploadBean;
import com.mosambee.bean.SnapbizUploadBean;
import com.mosambee.constants.BulkUploadCategory;
import com.mosambee.constants.BulkUploadFileLocation;
import com.mosambee.dao.SnapbizUploadDao;
import com.mosambee.properties.ExcelHeaderProperties;
import com.mosambee.service.ExcelService;
import com.mosambee.service.SnapbizUploadService;
import com.mosambee.validator.SnapbizUploadValidator;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class SnapbizUploadServiceImpl implements  SnapbizUploadService{
	
	@Autowired
	private ExcelService excelService;
	@Autowired
	private SnapbizUploadValidator validator;
	@Autowired
	private SnapbizUploadDao dao;
	
	@Autowired
	private ExcelHeaderProperties excelHeaderProperties;
	
	private static final DataFormatter dataFormatter = new DataFormatter();

	@Override
	public Resource getBulkUploadFormat() {
		ClassPathResource resource = null;
		resource = new ClassPathResource(BulkUploadFileLocation.SNAPBIZ_INSERT_UPLOAD.get());
		return resource.exists() ? resource : null;
	}
	
	@Override
	public Resource processBulkUploadExcel(MultipartFile file) {
		CustomUser user = (CustomUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		String userId = user.getMyMap().get("id");
		
		Workbook workbook = excelService.getWorkbookFromMultipartFile(file);

		List<SnapbizUploadBean> bulkUploadBeanList = new ArrayList<>();
		List<SnapbizUploadBean> successRecordList = new ArrayList<>();
		List<SnapbizUploadBean> failedRecordList = new ArrayList<>();

		// VALIDATE THE HEADER & VALIDATE SECURITY ISSUES
		if (validateExcelFile(file, workbook) && validateSecurityIssues(file, workbook)) {

			// PARSE DATA FROM WORKBOOK
			List<SnapbizUploadBean> beanList = parseBulkUploadFields(workbook);

			// VALIDATE THE PARSED DATA FROM WORKBOOK INTO SUCCESS & FAILED RECORD LIST
			validateAndTransformParsedDataIntoLists(beanList, successRecordList, failedRecordList);

			persist(successRecordList,userId);

			// ADD ALL SUCCESS AND FAILED RECORDS IN A COMMON LIST
			bulkUploadBeanList.addAll(successRecordList);
			bulkUploadBeanList.addAll(failedRecordList);
			// GET THE EXCEL WITH RESPONSE
			Workbook responseWorkbook = writeBulkUploadBeanListToExcel(bulkUploadBeanList);
			log.info("processBulkUploadExcel responseWorkbook  {}", responseWorkbook);

			Resource resource = excelService.getResourceFromWorkbook(responseWorkbook);

			try {
				responseWorkbook.close();
			} catch (IOException e) {

				log.info("error occured {}", e);
			}

			return resource;

		} else {
			log.error("Excel file is not valid");
			return null;
		}
	}

		private Workbook writeBulkUploadBeanListToExcel(List<SnapbizUploadBean> bulkUploadBeanList) {
		// CREATE THE WORKBOOK AND WRITE THE HEADER WITH CUSTOM STYLING

		log.info("Inside writeBulkUploadBeanListToExcel bulkUploadBeanList ::{}", bulkUploadBeanList);
		Workbook workbook = excelService.createHeaderRow(BulkUploadCategory.SNAPBIZ_INSERT_UPLOAD);
		Sheet sheet = workbook.getSheetAt(0);
		if (bulkUploadBeanList.isEmpty()) {
			Row row = sheet.createRow(1);
			row.createCell(5).setCellValue("Please provide Excel Data.");
		}

		// WRITE THE DATA TO THE EXCEL FILE
		int rowNum = 1;
		for (SnapbizUploadBean snapbizUploadBean : bulkUploadBeanList) {
			Row row = sheet.createRow(rowNum++);
			row.createCell(0).setCellValue(snapbizUploadBean.getPosId());
			row.createCell(1).setCellValue(snapbizUploadBean.getTerminalId());
			row.createCell(2).setCellValue(snapbizUploadBean.getStoreId());
			row.createCell(3).setCellValue(snapbizUploadBean.getStoreIdCreationDate());
			row.createCell(4).setCellValue(snapbizUploadBean.getStatus());
			row.createCell(5).setCellValue(snapbizUploadBean.getReason());
			log.info("inside writeBulkUploadBeanListToExcel :{}", snapbizUploadBean);

		}
		excelService.autoSizeExcel(workbook);
		return workbook;
	}

	private void persist(List<SnapbizUploadBean> snapbizUploadBeanList, String userId) {
		log.info("persist {}", snapbizUploadBeanList.size());
		// INSERT INTO DB
		for (SnapbizUploadBean snapbizUploadBean : snapbizUploadBeanList) {
			log.info("Persist loop{}",snapbizUploadBean);
			dao.validateSnapbizBean(snapbizUploadBean,userId);

		}

	}

	private void validateAndTransformParsedDataIntoLists(List<SnapbizUploadBean> beanList,
			List<SnapbizUploadBean> successRecordList, List<SnapbizUploadBean> failedRecordList) {
		log.info("inside validateAndTransformParsedDataIntoLists");

		// LOOP OVER THE LIST
		for (SnapbizUploadBean Bean : beanList) {
			Bean.setReason("");
			// VALIDATE THE BEAN
			SnapbizUploadBean uploadBean = validator.validateSnapbizUploadBean(Bean);
			// PUT THEM IN RESPECTIVE LIST.
			if (uploadBean.getReason().equals("")) {
				successRecordList.add(uploadBean);
			} else {
				failedRecordList.add(uploadBean);
			}
		}

	}

	private List<SnapbizUploadBean> parseBulkUploadFields(Workbook workbook) {
		Iterator<Row> rowIterator = workbook.getSheetAt(0).iterator();
		List<SnapbizUploadBean> beanList = new ArrayList<>();

		// SKIPING THE HEADER ROW
		rowIterator.next();

		while (rowIterator.hasNext()) {
			Row row = rowIterator.next();
			SnapbizUploadBean bean = parseRow(row);
			beanList.add(bean);
		}
		log.info("inside parseBulkUploadFields beanList {} ", beanList);
		return beanList;
	}

	private SnapbizUploadBean parseRow(Row row) {
		SnapbizUploadBean snapbizUploadBean = new SnapbizUploadBean();

		// SUBTRACT 2 FROM THE SIZE AS IT HAS STATUS,REASON HEADER.
		int sizeOfExcelHeader = excelHeaderProperties.getSnapbizInsertUploadHeaders().size() - 2;
		log.error("sizeOfExcelHeader  {}", sizeOfExcelHeader);
		for (int i = 0; i < sizeOfExcelHeader; ++i) {
			
			Cell cell = row.getCell(i);

			switch(i) {
			case 0:
				snapbizUploadBean.setPosId(dataFormatter.formatCellValue(cell));
				break;
			case 1:
				snapbizUploadBean.setTerminalId(dataFormatter.formatCellValue(cell));
				break;
			case 2:
				snapbizUploadBean.setStoreId(dataFormatter.formatCellValue(cell));
				break;
			case 3:
				snapbizUploadBean.setStoreIdCreationDate(dataFormatter.formatCellValue(cell));
				break;
			default:
				String defaultCellValue = dataFormatter.formatCellValue(cell);
				log.info("Getting into the default case: {}", defaultCellValue);
				break;
			}

		}
		return snapbizUploadBean;
	}


	private boolean validateSecurityIssues(MultipartFile file, Workbook workbook) {
		return excelService.checkForEmbeddedObjects(workbook) && excelService.checkForMacrosAndVbScript(file);
	}

	private boolean validateExcelFile(MultipartFile file, Workbook workbook) {
		return excelService.validateExtension(file)
				&& excelService.validateHeader(workbook, BulkUploadCategory.SNAPBIZ_INSERT_UPLOAD);
	}

}
