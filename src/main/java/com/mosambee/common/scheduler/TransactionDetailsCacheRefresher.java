package com.mosambee.common.scheduler;

import com.mosambee.common.cache.TransactionDetailsCache;
import com.mosambee.dao.CommonDao;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Log4j2
@Component
public class TransactionDetailsCacheRefresher {

    @Autowired
    CommonDao commonDao;

    @PostConstruct
    public void loadTransactionDetails() {
        try {
            Map<Integer, String> transactionTypeIdAndNameMap = commonDao.getTransactionTypeIdAndNameMap();
            TransactionDetailsCache.loadTransactionDetails(transactionTypeIdAndNameMap);
            log.info("Transaction details cache refreshed successfully with {} entries", transactionTypeIdAndNameMap.size());
        } catch (Exception e) {
            log.error("Failed to refresh transaction details cache", e);
        }
    }
}
