package com.mosambee.transformer;

import com.mosambee.bean.BinCategoryMappingBean;
import com.mosambee.bean.BinCategoryUploadBean;
import com.mosambee.bean.BinDetailsUploadBean;

public interface BinUploadTransformer {
	void transformbin(BinDetailsUploadBean binDetails);

	void transformbinlow(BinDetailsUploadBean binDetails);

	void transformbinhigh(BinDetailsUploadBean binDetails);

	void transformcardType(BinDetailsUploadBean binDetails);

	void transformproductId(BinDetailsUploadBean binDetails);

	void transformcardBrand(BinDetailsUploadBean binDetails);

	void transformcountryCodeNum(BinDetailsUploadBean binDetails);

	void transformtype(BinDetailsUploadBean binDetails);

	void transformissuerBankName(BinDetailsUploadBean binDetails);

	void transformissuerCountry(BinDetailsUploadBean binDetails);

	void transformfullCardType(BinDetailsUploadBean binDetails);

	void transformbinType(BinDetailsUploadBean binDetails);

	void transformTypeName(BinDetailsUploadBean binDetails);

	void transformName(BinCategoryUploadBean binCategoryUploadBean);

	void transformCode(BinCategoryUploadBean binCategoryUploadBean);

	void transformDescription(BinCategoryUploadBean binCategoryUploadBean);

	void transformStatus(BinCategoryUploadBean binCategoryUploadBean);

	void transformStartDate(BinCategoryUploadBean binCategoryUploadBean);

	void transformEndDate(BinCategoryUploadBean binCategoryUploadBean);

	void transformBinCategoryType(BinCategoryUploadBean binCategoryUploadBean);

	void transformBinLength(BinCategoryUploadBean binCategoryUploadBean);

	void transformBinCategoryCode(BinCategoryMappingBean binCategoryMappingBean);

	void transformBinCategoryMappingStatus(BinCategoryMappingBean binCategoryMappingBean);

	void transformBinValue(BinCategoryMappingBean binCategoryMappingBean);

	void transformBinRangeStart(BinCategoryMappingBean binCategoryMappingBean);

	void transformBinRangeEnd(BinCategoryMappingBean binCategoryMappingBean);
}