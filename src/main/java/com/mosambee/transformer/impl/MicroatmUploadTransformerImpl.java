package com.mosambee.transformer.impl;
import org.springframework.stereotype.Component;

import com.mosambee.bean.MicroatmUploadBean;
import com.mosambee.transformer.MicroatmUploadTransformer;

/**
 * This class is responsible for trimming space coming from terminal users
 * upload field
 *
 * <AUTHOR>
 * @version 1.0
 *
 */

@Component("microatmUploadTransformer")
public class MicroatmUploadTransformerImpl implements MicroatmUploadTransformer {



	/**
	 * transformAcquirer() is responsible for trimming space coming from mid
	 *
	 * @param UploadBankDetailBean
	 */
	@Override
	public void transformPosId(MicroatmUploadBean microatmUploadBean) {
		String posId = microatmUploadBean.getPosId();
		posId = posId.trim();
		microatmUploadBean.setPosId(posId);
	}

	/**
	 * transformMid() is responsible for trimming space coming from
	 * terminalId
	 *
	 * @param MicroatmUploadBean
	 */
	@Override
	public void transformMid(MicroatmUploadBean microatmUploadBean) {
		String mid = microatmUploadBean.getMid();
		mid = mid.trim();
		microatmUploadBean.setMid(mid);
	}

	/**
	 * transformTid() is responsible for trimming space coming from
	 * terminalId
	 *
	 * @param MicroatmUploadBean
	 */
	@Override
	public void transformTid(MicroatmUploadBean microatmUploadBean) {
		String tid = microatmUploadBean.getTid();
		tid = tid.trim();
		microatmUploadBean.setTid(tid);
	}

	/**
	 * transformTid() is responsible for trimming space coming from location
	 *
	 * @param UploadBankDetailBean
	 */
	@Override
	public void transformUsername(MicroatmUploadBean microatmUploadBean) {
		String userName = microatmUploadBean.getUserName();
		userName = userName.trim();
		microatmUploadBean.setUserName(userName);
	}

	/**
	 * transformUserNo() is responsible for trimming space coming from type
	 *
	 * @param UploadBankDetailBean
	 */
	@Override
	public void transformCashWithdrawal(MicroatmUploadBean microatmUploadBean) {
		String cashWithdrawal = microatmUploadBean.getCashWithdrawal();
		cashWithdrawal = cashWithdrawal.trim();
		microatmUploadBean.setCashWithdrawal(cashWithdrawal);
	}

	/**
	 * transformCity() is responsible for trimming space coming from sale Received
	 *
	 * @param UploadBankDetailBean
	 */
	@Override
	public void transformBalanceEnquiry(MicroatmUploadBean microatmUploadBean) {
		String balanceEnquiry = microatmUploadBean.getBalanceEnquiry();
		balanceEnquiry = balanceEnquiry.trim();
		microatmUploadBean.setBalanceEnquiry(balanceEnquiry);
	}

	/**
	 * transformTidReceived() is responsible for trimming space coming from void
	 *
	 * @param UploadBankDetailBean
	 */
	@Override
	public void transformMatCode(MicroatmUploadBean microatmUploadBean) {
		String matCode = microatmUploadBean.getMatCode();
		matCode = matCode.trim();
		microatmUploadBean.setMatCode(matCode);
	}


	/**
	 * transformName() is responsible for trimming space coming from void
	 *
	 * @param MicroatmUploadBean
	 */
	@Override
	public void transformName(MicroatmUploadBean microatmUploadBean) {
		String name  = microatmUploadBean.getName();
		name = name.trim();
		microatmUploadBean.setName(name);
	}

	/**
	 * transformLocation() is responsible for trimming space coming from void
	 *
	 * @param UploadBankDetailBean
	 */
	@Override
	public void transformLocation(MicroatmUploadBean microatmUploadBean) {
		String location = microatmUploadBean.getLocation();
		location = location.trim();
		microatmUploadBean.setLocation(location);
	}


	/**
	 * transformTidType() is responsible for trimming space coming from void
	 *
	 * @param MicroatmUploadBean
	 */
	@Override
	public void transformTidType(MicroatmUploadBean microatmUploadBean) {
		String tidType = microatmUploadBean.getTidType();
		tidType = tidType.trim();
		microatmUploadBean.setTidType(tidType);
	}

}

