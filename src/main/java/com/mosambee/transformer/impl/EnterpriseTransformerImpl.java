package com.mosambee.transformer.impl;

import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;
import com.mosambee.bean.EnterpriseDataBean;
import com.mosambee.bean.EnterpriseTransactionData;
import com.mosambee.bean.EnterpriseUserDataBean;
import com.mosambee.bean.TransactionSearchFormDataTableBean;
import com.mosambee.bean.datatables.DataTablesRequest;
import com.mosambee.constants.ColumnNames;
import com.mosambee.transformer.EnterpriseTransformer;
import lombok.extern.log4j.Log4j2;

/**
 * EnterpriseTransformerImpl is responsible for transforming the EnterpriseBean
 * fields ... Mainly implemented for transforming the search inputs coming via
 * data-tables.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 01-April-2020
 */
@Log4j2
@Component("enterpriseTransformer")
public class EnterpriseTransformerImpl implements EnterpriseTransformer{

	/**
	 * transformEnterprise(...) is responsible for transforming incoming
	 * {@link DataTablesRequest} ... We are mainly trimming values here.
	 * 
	 * @param dtRequest {@link DataTablesRequest}
	 * @return Map<String, String>
	 */
	@Override
	public Map<String, String> transformEnterprise(EnterpriseDataBean dtRequest) {
		Map<String, String> searchMap = new HashMap<>();

		searchMap.put(ColumnNames.ENTERPRISE_FROM_DATE.get(), transformEnterpriseFromDate(dtRequest));
		searchMap.put(ColumnNames.ENTERPRISE_TO_DATE.get(), transformEnterpriseToDate(dtRequest));
		
		return searchMap;
	}
	
	/**
	 * transformEnterpriseFromDate(...) is responsible for trimming the description
	 * field.
	 * 
	 * @param dtRequest
	 * @return String
	 */
	private String transformEnterpriseFromDate(EnterpriseDataBean dtRequest) {
		String transactionFromDate = dtRequest.getFromDate();
		return transactionFromDate.trim();
	}
	
	/**
	 * transformEnterpriseToDate(...) is responsible for trimming the description
	 * field.
	 * 
	 * @param dtRequest
	 * @return String
	 */
	private String transformEnterpriseToDate(EnterpriseDataBean dtRequest) {
		String transactionToDate = dtRequest.getToDate();
		return transactionToDate.trim();
	}
	
	/**
	 * transformTransactionListDataTableRequest(...) is responsible for transforming incoming
	 * {@link TransactionSearchFormDataTableBean} ... We are mainly trimming values here.
	 * 
	 * @param dtRequest {@link DataTablesRequest}
	 * @return Map<String, String>
	 */
	@Override
	public Map<String, String> transformTransactionListDataTableRequest(EnterpriseTransactionData dtRequest) {
		Map<String, String> searchMap = new HashMap<>();

		searchMap.put(ColumnNames.TXN_LIST_USER_ID.get(), transformTxnUserId(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_MERCHANT_NAME.get(), transformTxnMerchantName(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_TXN_ID.get(), transformTxnTxnId(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_RRN.get(), transformTxnRRN(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_AUTOCODE.get(), transformTxnAuthCode(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_CARD_NUMBER.get(), transformTxnCardNumber(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_CARD_HOLDER_NAME.get(), transformTxnCardHolderName(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_ACQUIRER.get(), transformTxnAcquirer(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_MID.get(), transformTxnMID(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_TID.get(), transformTxnTID(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_DATE_FROM.get(), transformTxnDateFrom(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_DATE_TO.get(), transformTxnDateTo(dtRequest));
		searchMap.put(ColumnNames.TXN_LIST_TXN_TYPE.get(), transformTxnTxnType(dtRequest));

		return searchMap;
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the userid 
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnUserId(EnterpriseTransactionData dtRequest) {
		return dtRequest.getUserId().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the MerchantName 
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnMerchantName(EnterpriseTransactionData dtRequest) {
		return dtRequest.getMerchantName().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the transaction id 
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnTxnId(EnterpriseTransactionData dtRequest) {	    	
	    return dtRequest.getTransactionId().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the rrn 
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnRRN(EnterpriseTransactionData dtRequest) {
		return dtRequest.getRrn().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the auth code 
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnAuthCode(EnterpriseTransactionData dtRequest) {
		return dtRequest.getAuthCode().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the msked card number 
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnCardNumber(EnterpriseTransactionData dtRequest) {
		return dtRequest.getMaskedCardNumber().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the card holder name 
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnCardHolderName(EnterpriseTransactionData dtRequest) {
		return dtRequest.getCardHolderName().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the acquirer
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnAcquirer(EnterpriseTransactionData dtRequest) {
		return dtRequest.getAcquirer().trim();
	}
	
	
	/**
	 * transformTxnUserId is responsible for trimming the mid
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnMID(EnterpriseTransactionData dtRequest) {
		return dtRequest.getMid().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the tid
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnTID(EnterpriseTransactionData dtRequest) {
		return dtRequest.getTid().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the date from
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnDateFrom(EnterpriseTransactionData dtRequest) {
		return dtRequest.getTransactionDateFrom().trim();
	}
	
	/**
	 * transformTxnUserId is responsible for trimming the date to
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnDateTo(EnterpriseTransactionData dtRequest) {
		return dtRequest.getTransactionDateTo().trim();
	}
	
	
	/**
	 * transformTxnUserId is responsible for trimming the txn type
	 * @param dtRequest
	 * @return
	 */
	private String transformTxnTxnType(EnterpriseTransactionData dtRequest) {
		log.info("txn type {}",dtRequest.getTxnType());
		String txnType="";
		if(dtRequest.getTxnType().equals("101")) {
			txnType = "3"; 
		}
		
		if(dtRequest.getTxnType().equals("102")) {
			txnType = "-2"; 
		}
		
		if(dtRequest.getTxnType().equals("103")) {
			txnType = "0"; 
		}
		
		if(dtRequest.getTxnType().equals("104")) {
			txnType = "20"; 
		}
		
		if(!dtRequest.getTxnType().equals("101") && !dtRequest.getTxnType().equals("102") && !dtRequest.getTxnType().equals("103") && !dtRequest.getTxnType().equals("104")) {
		   return dtRequest.getTxnType().trim();
		}else {
			return txnType;
		}
	} 
	
	/**
	 * transformEnterpriseUser(...) is responsible for transforming incoming
	 * {@link EnterpriseUserDataBean} ... We are mainly trimming values here.
	 * 
	 * @param dtRequest {@link DataTablesRequest}
	 * @return Map<String, String>
	 */
	@Override
	public Map<String, String> transformEnterpriseUser(EnterpriseUserDataBean dtRequest) {
		Map<String, String> searchMap = new HashMap<>();

		searchMap.put(ColumnNames.ENTERPRISE_USER_FIRSTNAME.get(), transformFirstName(dtRequest));
		searchMap.put(ColumnNames.ENTERPRISE_USER_LASTNAME.get(), transformLastName(dtRequest));
		searchMap.put(ColumnNames.ENTERPRISE_USER_EMAIL.get(), transformEmail(dtRequest));

		return searchMap;
	}
	
	/**
	 * transformFirstName is responsible for trimming the first name
	 * @param dtRequest
	 * @return
	 */
	private String transformFirstName(EnterpriseUserDataBean dtRequest) {
		return dtRequest.getFirstName().trim();
	}
	
	/**
	 * transformLastName is responsible for trimming the first name
	 * @param dtRequest
	 * @return
	 */
	private String transformLastName(EnterpriseUserDataBean dtRequest) {
		return dtRequest.getLastName().trim();
	}
	
	/**
	 * transformEmail is responsible for trimming the first name
	 * @param dtRequest
	 * @return
	 */
	private String transformEmail(EnterpriseUserDataBean dtRequest) {
		return dtRequest.getEmail().trim();
	}
	
}
